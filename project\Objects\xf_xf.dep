Dependencies for Project 'xf', Target 'xf': (DO NOT MODIFY !)
CompilerVersion: 6190000::V6.19::ARMCLANG
F (..\lib\ti\driverlib\dl_adc12.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_adc12.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_aes.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aes.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_aesadv.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aesadv.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_common.c)(0x667FEDEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_common.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_crc.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crc.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_crcp.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crcp.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_dac12.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dac12.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_dma.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dma.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_flashctl.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_flashctl.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_i2c.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_i2c.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_keystorectl.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_keystorectl.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_lcd.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lcd.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_lfss.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lfss.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_mathacl.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mathacl.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_mcan.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mcan.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_opa.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_opa.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_rtc_common.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_rtc_common.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_spi.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_spi.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_timer.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_timer.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_trng.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_trng.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_uart.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_uart.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
F (..\lib\ti\driverlib\dl_vref.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_vref.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\m0p\dl_interrupt.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_interrupt.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
F (..\lib\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.c)(0x66631CE6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_sysctl_mspm0g1x0x_g3x0x.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\lib\ti\driverlib\lib\keil\m0p\mspm0g1x0x_g3x0x\driverlib.a)(0x66631CE6)()
F (..\user\main.c)(0x68862CDF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/main.o -MD)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
F (..\user\ti_msp_dl_config.c)(0x688738F5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MD)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
F (..\user\ti_msp_dl_config.h)(0x688738F4)()
F (..\user\main.h)(0x688735CE)()
F (..\user\main.syscfg)(0x688738F4)()
F (..\lib\ti\devices\msp\m0p\startup_system_files\keil\startup_mspm0g350x_uvision.s)(0x68594A5A)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4 -Wa,armasm,--pd,"__MICROLIB SETA 1"

-Wa,armasm,--pd,"__UVISION_VERSION SETA 538" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (..\FreeRTOS\croutine.c)(0x64020E50)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/croutine.o -MD)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\croutine.h)(0x64020E50)
F (..\FreeRTOS\event_groups.c)(0x64020E50)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/event_groups.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\stdlib.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\timers.h)(0x64020E50)
I (..\FreeRTOS\include\event_groups.h)(0x64020E50)
F (..\FreeRTOS\list.c)(0x64020E50)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/list.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\stdlib.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
F (..\FreeRTOS\queue.c)(0x64020E50)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/queue.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\stdlib.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\string.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
F (..\FreeRTOS\readme.txt)(0x5238100C)()
F (..\FreeRTOS\tasks.c)(0x667D341E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/tasks.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\stdlib.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\string.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\timers.h)(0x64020E50)
I (..\FreeRTOS\include\stack_macros.h)(0x64020E50)
F (..\FreeRTOS\timers.c)(0x64020E50)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/timers.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\stdlib.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\FreeRTOS\include\timers.h)(0x64020E50)
F (..\FreeRTOS\portable\MemMang\heap_4.c)(0x6665A024)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/heap_4.o -MD)
I (H:\app\keil5\ARM\ARMCLANG\include\stdlib.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\string.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
F (..\FreeRTOS\portable\ARM_CM0\port.c)(0x64020E50)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/port.o -MD)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
F (..\Task\StartupTask.c)(0x68871CB1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/startuptask.o -MD)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
F (..\Hardware\ZDT\Emm_V5.c)(0x6884DC62)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/emm_v5.o -MD)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
F (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)()
F (..\Hardware\ws2812\ws2812b.c)(0x685943F7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ws2812b.o -MD)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
F (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)()
F (..\Hardware\UART\UART.c)(0x68849684)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/uart.o -MD)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
F (..\Hardware\UART\UART.h)(0x68849689)()
F (..\Hardware\servo\servo.c)(0x6884D02E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/servo.o -MD)
I (..\Hardware\servo\servo.h)(0x6884CDC6)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
F (..\Hardware\servo\servo.h)(0x6884CDC6)()
F (..\Hardware\oled\oled.c)(0x6884BFC8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/oled.o -MD)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
I (..\Hardware\oled\OLED_Font.h)(0x685950FE)
I (H:\app\keil5\ARM\ARMCLANG\include\stdarg.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\string.h)(0x63884908)
F (..\Hardware\oled\oled.h)(0x6884BF76)()
F (..\Hardware\oled\OLED_Font.h)(0x685950FE)()
F (..\Hardware\Motor\Motor.c)(0x68846637)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/motor.o -MD)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
F (..\Hardware\Motor\Motor.h)(0x688457CB)()
F (..\Hardware\LED_Key\LED_Key.c)(0x6698B417)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/led_key.o -MD)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
F (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)()
F (..\Hardware\Enconder\Enconder.c)(0x6885E3E2)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/enconder.o -MD)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
F (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)()
F (..\Hardware\delay\delay.c)(0x6884E32F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/delay.o -MD)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
F (..\Hardware\delay\delay.h)(0x6884E17E)()
F (..\Hardware\control\control.c)(0x68873BE0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/control.o -MD)
I (..\Hardware\control\control.h)(0x68873CEB)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
F (..\Hardware\MaixCam\MaixCam.c)(0x6885F467)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -w -I ../lib/ti/CMSIS -I ../FreeRTOS -I ../FreeRTOS/include -I ../user -I ../FreeRTOS/portable/ARM_CM0 -I ../Task -I ../Hardware/ZDT -I ../Hardware/ws2812 -I ../Hardware/UART -I ../Hardware/Timer -I ../Hardware/servo -I ../Hardware/PID -I ../Hardware/oled -I ../Hardware/Motor -I ../Hardware/LED_Key -I ../Hardware/Enconder -I ../Hardware/IMU -I ../Hardware/delay -I ../Hardware/control -I ../Hardware/MaixCam

-D__UVISION_VERSION="538" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/maixcam.o -MD)
I (..\Hardware\MaixCam\MaixCam.h)(0x6885E433)
I (..\user\main.h)(0x688735CE)
I (..\user\ti_msp_dl_config.h)(0x688738F4)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\msp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\m0p\mspm0g350x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stdint.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\stdbool.h)(0x63884908)
I (..\lib\ti\CMSIS\core_cm0plus.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_version.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_compiler.h)(0x66631CE6)
I (..\lib\ti\CMSIS\cmsis_armclang.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_compat.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\arm_acle.h)(0x63884908)
I (..\lib\ti\CMSIS\mpu_armv7.h)(0x66631CE6)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\driverlib.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\stddef.h)(0x63884908)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timera.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timer.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_timerg.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (H:\app\keil5\ARM\ARMCLANG\include\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\FreeRTOS\include\FreeRTOS.h)(0x666597A0)
I (..\FreeRTOS\include\FreeRTOSConfig.h)(0x6859479C)
I (H:\app\keil5\ARM\ARMCLANG\include\stdio.h)(0x63884908)
I (..\FreeRTOS\include\projdefs.h)(0x64020E50)
I (..\FreeRTOS\include\portable.h)(0x64020E50)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x64020E50)
I (..\FreeRTOS\portable\ARM_CM0\portmacro.h)(0x64020E50)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x64020E50)
I (..\FreeRTOS\include\task.h)(0x64020E50)
I (..\FreeRTOS\include\list.h)(0x667BB9E4)
I (..\FreeRTOS\include\queue.h)(0x64020E50)
I (..\Task\StartupTask.h)(0x6884446A)
I (..\Hardware\delay\delay.h)(0x6884E17E)
I (..\Hardware\LED_Key\LED_Key.h)(0x688738F4)
I (..\Hardware\IMU\IMU.h)(0x6854BC6C)
I (..\Hardware\UART\UART.h)(0x68849689)
I (..\Hardware\ZDT\Emm_V5.h)(0x6854BEF5)
I (..\Hardware\Timer\Timer.h)(0x669A1ABA)
I (..\Hardware\Enconder\Enconder.h)(0x6856D5DA)
I (..\Hardware\Motor\Motor.h)(0x688457CB)
I (..\Hardware\oled\oled.h)(0x6884BF76)
I (..\Hardware\servo\Servo.h)(0x6884CDC6)
I (..\Hardware\ws2812\ws2812b.h)(0x6854BC6C)
I (..\Hardware\control\control.h)(0x68873CEB)
