#ifndef __CONTROL_H
#define __CONTROL_H

#include "main.h"                  // Device header

#define SPEED    1700            //直线速度
#define GAIN     600             //偏差增益
#define GAIN_K  2              //大角度偏差增益

#define TURN_SPEED    1600            //旋转速度
#define LEFT_OR        -1             //左旋转
#define RIGHT_OR        1             //右旋转

#define MOTOR_Line        1           //巡线模式(高级)
#define MOTOR_Line_Simple 2           //巡线模式(简单)
#define MOTOR_TURN        3           //旋转模式

extern struct PID Serx,Sery;

struct PID
{
	float kp;
	float ki;
	float kd;
	
	float err;
	float err_last;
	float err_last_dev;
	float err_last_dev_last;
	float err_last_dev_lastdev;
	float err_add;
	
	float ki_p;
	
	float out;
};

float Erect_pid(struct PID* para,float hope, float now);
void PID_Init(void);
void Motor_Open(int Mode);
void Line_Control(void);
void Line_Control_Simple(void);  // 简单循迹控制
void Line_PID_Init(void);        // 循迹PID初始化
void Get_hw(void);
float Get_Line_Position(void);   // 获取线条位置
uint8_t Get_Sensor_Count(void);  // 获取触发传感器数量
void Set_Line_PID_Params(float kp, float ki, float kd);  // 设置PID参数
float Get_PID_Output(void);      // 获取PID输出值
void Reset_Line_PID(void);       // 重置PID状态


#endif
