Component: Arm Compiler for Embedded 6.19 Tool: armlink [5e73cb00]

==============================================================================

Section Cross References

    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKey) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOut) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorData) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration) refers to dl_aes.o(.text.DL_AES_saveConfiguration) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration) refers to dl_aes.o(.text.DL_AES_restoreConfiguration) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_calculateBlock32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_calculateBlock16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange16) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_init) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.ramfunc) refers to dl_flashctl.o(.ramfunc) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for DL_FlashCTL_eraseDataBankFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryReset) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for DL_FlashCTL_eraseDataBankFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for DL_FlashCTL_massEraseMultiBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_protectSector) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerify) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_configOperation) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady) refers to dl_mcan.o(.text.DL_MCAN_isReady) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_MCAN_setClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig) refers to dl_mcan.o(.text.DL_MCAN_getClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.DL_MCAN_isInReset) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.DL_MCAN_isFDOpEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.DL_MCAN_isMemInitDone) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.DL_MCAN_setOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.DL_MCAN_getOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_init) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_config) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_setBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_msgRAMConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_TXBufAddReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_getNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.DL_MCAN_getErrCounters) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.DL_MCAN_getProtocolStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.DL_MCAN_enableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.DL_MCAN_selectIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.DL_MCAN_enableIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_clearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.DL_MCAN_addClockStopRequest) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccForceError) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_eccWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_eccEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.DL_MCAN_getRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClockStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.DL_MCAN_getRxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_setTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.DL_MCAN_getTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTSCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClkStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.DL_MCAN_getBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.DL_MCAN_resetTSCounter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTOCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.DL_MCAN_getEndianVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration) refers to dl_mcan.o(.text.DL_MCAN_saveConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_MCAN_restoreConfiguration) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_increaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_decreaseGain) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration) refers to dl_trng.o(.text.DL_TRNG_saveConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_configReference) refers to dl_vref.o(.text.DL_VREF_configReference) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig) refers to dl_vref.o(.text.DL_VREF_setClockConfig) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig) refers to dl_vref.o(.text.DL_VREF_getClockConfig) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to startup_mspm0g350x_uvision.o(.text) for Default_Handler
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    main.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    main.o(.text.main) refers to maixcam.o(.text.MaixCam_Init) for MaixCam_Init
    main.o(.text.main) refers to oled.o(.text.OLED_Init) for OLED_Init
    main.o(.text.main) refers to emm_v5.o(.text.ZDT_Init) for ZDT_Init
    main.o(.text.main) refers to enconder.o(.text.Encoder_init) for Encoder_init
    main.o(.text.main) refers to emm_v5.o(.text.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    main.o(.text.main) refers to delay.o(.text.delay_noOS) for delay_noOS
    main.o(.text.main) refers to emm_v5.o(.text.Emm_V5_En_Control) for Emm_V5_En_Control
    main.o(.text.main) refers to startuptask.o(.text.StartupTask) for StartupTask
    main.o(.text.main) refers to tasks.o(.text.vTaskStartScheduler) for vTaskStartScheduler
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) for SYSCFG_DL_MOTOR_PWM_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) for SYSCFG_DL_Servo_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init) for SYSCFG_DL_ZDT1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for SYSCFG_DL_UART_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) for SYSCFG_DL_SPI_WS2812_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gServoBackup) for gServoBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gSPI_WS2812Backup) for gSPI_WS2812Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for DL_Timer_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for DL_TimerA_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to ti_msp_dl_config.o(.rodata.gServoClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to ti_msp_dl_config.o(.rodata.gServoConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_Servo_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init) refers to ti_msp_dl_config.o(.rodata.gZDT1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init) refers to ti_msp_dl_config.o(.rodata.gZDT1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ZDT1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for DL_SPI_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) refers to dl_spi.o(.text.DL_SPI_init) for DL_SPI_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) refers to ti_msp_dl_config.o(.rodata.gSPI_WS2812_clockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) refers to ti_msp_dl_config.o(.rodata.gSPI_WS2812_config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_WS2812_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for DL_SPI_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gServoBackup) for gServoBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_WS2812Backup) for gSPI_WS2812Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for DL_SPI_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gServoBackup) for gServoBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gSPI_WS2812Backup) for gSPI_WS2812Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to port.o(.text.SVC_Handler) for SVC_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to port.o(.text.PendSV_Handler) for PendSV_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to port.o(.text.SysTick_Handler) for SysTick_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to enconder.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to maixcam.o(.text.UART1_IRQHandler) for UART1_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    event_groups.o(.text.xEventGroupCreate) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    event_groups.o(.text.xEventGroupCreate) refers to list.o(.text.vListInitialise) for vListInitialise
    event_groups.o(.ARM.exidx.text.xEventGroupCreate) refers to event_groups.o(.text.xEventGroupCreate) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(.text.xEventGroupSync) refers to port.o(.text.vPortYield) for vPortYield
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(.text.xEventGroupSync) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(.text.xEventGroupSync) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    event_groups.o(.ARM.exidx.text.xEventGroupSync) refers to event_groups.o(.text.xEventGroupSync) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupSetBits) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(.text.xEventGroupSetBits) refers to tasks.o(.text.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(.text.xEventGroupSetBits) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(.ARM.exidx.text.xEventGroupSetBits) refers to event_groups.o(.text.xEventGroupSetBits) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupWaitBits) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(.text.xEventGroupWaitBits) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(.text.xEventGroupWaitBits) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(.text.xEventGroupWaitBits) refers to tasks.o(.text.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(.text.xEventGroupWaitBits) refers to port.o(.text.vPortYield) for vPortYield
    event_groups.o(.text.xEventGroupWaitBits) refers to tasks.o(.text.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(.text.xEventGroupWaitBits) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(.text.xEventGroupWaitBits) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    event_groups.o(.ARM.exidx.text.xEventGroupWaitBits) refers to event_groups.o(.text.xEventGroupWaitBits) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupClearBits) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(.text.xEventGroupClearBits) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    event_groups.o(.ARM.exidx.text.xEventGroupClearBits) refers to event_groups.o(.text.xEventGroupClearBits) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupClearBitsFromISR) refers to timers.o(.text.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(.text.xEventGroupClearBitsFromISR) refers to event_groups.o(.text.vEventGroupClearBitsCallback) for vEventGroupClearBitsCallback
    event_groups.o(.ARM.exidx.text.xEventGroupClearBitsFromISR) refers to event_groups.o(.text.xEventGroupClearBitsFromISR) for [Anonymous Symbol]
    event_groups.o(.text.vEventGroupClearBitsCallback) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(.text.vEventGroupClearBitsCallback) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    event_groups.o(.ARM.exidx.text.vEventGroupClearBitsCallback) refers to event_groups.o(.text.vEventGroupClearBitsCallback) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupGetBitsFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    event_groups.o(.text.xEventGroupGetBitsFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    event_groups.o(.ARM.exidx.text.xEventGroupGetBitsFromISR) refers to event_groups.o(.text.xEventGroupGetBitsFromISR) for [Anonymous Symbol]
    event_groups.o(.text.vEventGroupDelete) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(.text.vEventGroupDelete) refers to tasks.o(.text.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(.text.vEventGroupDelete) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(.text.vEventGroupDelete) refers to heap_4.o(.text.vPortFree) for vPortFree
    event_groups.o(.ARM.exidx.text.vEventGroupDelete) refers to event_groups.o(.text.vEventGroupDelete) for [Anonymous Symbol]
    event_groups.o(.text.vEventGroupSetBitsCallback) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(.text.vEventGroupSetBitsCallback) refers to tasks.o(.text.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(.text.vEventGroupSetBitsCallback) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(.ARM.exidx.text.vEventGroupSetBitsCallback) refers to event_groups.o(.text.vEventGroupSetBitsCallback) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupSetBitsFromISR) refers to timers.o(.text.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(.text.xEventGroupSetBitsFromISR) refers to event_groups.o(.text.vEventGroupSetBitsCallback) for vEventGroupSetBitsCallback
    event_groups.o(.ARM.exidx.text.xEventGroupSetBitsFromISR) refers to event_groups.o(.text.xEventGroupSetBitsFromISR) for [Anonymous Symbol]
    event_groups.o(.ARM.exidx.text.uxEventGroupGetNumber) refers to event_groups.o(.text.uxEventGroupGetNumber) for [Anonymous Symbol]
    event_groups.o(.ARM.exidx.text.vEventGroupSetNumber) refers to event_groups.o(.text.vEventGroupSetNumber) for [Anonymous Symbol]
    list.o(.ARM.exidx.text.vListInitialise) refers to list.o(.text.vListInitialise) for [Anonymous Symbol]
    list.o(.ARM.exidx.text.vListInitialiseItem) refers to list.o(.text.vListInitialiseItem) for [Anonymous Symbol]
    list.o(.ARM.exidx.text.vListInsertEnd) refers to list.o(.text.vListInsertEnd) for [Anonymous Symbol]
    list.o(.ARM.exidx.text.vListInsert) refers to list.o(.text.vListInsert) for [Anonymous Symbol]
    list.o(.ARM.exidx.text.uxListRemove) refers to list.o(.text.uxListRemove) for [Anonymous Symbol]
    queue.o(.text.xQueueGenericReset) refers to llmul.o(.text) for __aeabi_lmul
    queue.o(.text.xQueueGenericReset) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueGenericReset) refers to list.o(.text.vListInitialise) for vListInitialise
    queue.o(.text.xQueueGenericReset) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueueGenericReset) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.xQueueGenericReset) refers to port.o(.text.vPortYield) for vPortYield
    queue.o(.ARM.exidx.text.xQueueGenericReset) refers to queue.o(.text.xQueueGenericReset) for [Anonymous Symbol]
    queue.o(.text.xQueueGenericCreate) refers to llmul.o(.text) for __aeabi_lmul
    queue.o(.text.xQueueGenericCreate) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    queue.o(.text.xQueueGenericCreate) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueGenericCreate) refers to list.o(.text.vListInitialise) for vListInitialise
    queue.o(.text.xQueueGenericCreate) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.ARM.exidx.text.xQueueGenericCreate) refers to queue.o(.text.xQueueGenericCreate) for [Anonymous Symbol]
    queue.o(.text.xQueueCreateMutex) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    queue.o(.text.xQueueCreateMutex) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueCreateMutex) refers to list.o(.text.vListInitialise) for vListInitialise
    queue.o(.text.xQueueCreateMutex) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueueCreateMutex) refers to queue.o(.text.xQueueGenericSend) for xQueueGenericSend
    queue.o(.ARM.exidx.text.xQueueCreateMutex) refers to queue.o(.text.xQueueCreateMutex) for [Anonymous Symbol]
    queue.o(.text.xQueueGetMutexHolder) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueGetMutexHolder) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.ARM.exidx.text.xQueueGetMutexHolder) refers to queue.o(.text.xQueueGetMutexHolder) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.xQueueGetMutexHolderFromISR) refers to queue.o(.text.xQueueGetMutexHolderFromISR) for [Anonymous Symbol]
    queue.o(.text.xQueueGiveMutexRecursive) refers to tasks.o(.text.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(.text.xQueueGiveMutexRecursive) refers to queue.o(.text.xQueueGenericSend) for xQueueGenericSend
    queue.o(.ARM.exidx.text.xQueueGiveMutexRecursive) refers to queue.o(.text.xQueueGiveMutexRecursive) for [Anonymous Symbol]
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(.text.xQueueGenericSend) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueGenericSend) refers to queue.o(.text.prvUnlockQueue) for prvUnlockQueue
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(.text.xQueueGenericSend) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(.text.xQueueGenericSend) refers to port.o(.text.vPortYield) for vPortYield
    queue.o(.text.xQueueGenericSend) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(.ARM.exidx.text.xQueueGenericSend) refers to queue.o(.text.xQueueGenericSend) for [Anonymous Symbol]
    queue.o(.text.xQueueTakeMutexRecursive) refers to tasks.o(.text.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(.text.xQueueTakeMutexRecursive) refers to queue.o(.text.xQueueSemaphoreTake) for xQueueSemaphoreTake
    queue.o(.ARM.exidx.text.xQueueTakeMutexRecursive) refers to queue.o(.text.xQueueTakeMutexRecursive) for [Anonymous Symbol]
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(.text.xQueueSemaphoreTake) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.xQueueSemaphoreTake) refers to port.o(.text.vPortYield) for vPortYield
    queue.o(.text.xQueueSemaphoreTake) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueueSemaphoreTake) refers to queue.o(.text.prvUnlockQueue) for prvUnlockQueue
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    queue.o(.ARM.exidx.text.xQueueSemaphoreTake) refers to queue.o(.text.xQueueSemaphoreTake) for [Anonymous Symbol]
    queue.o(.text.xQueueCreateCountingSemaphore) refers to queue.o(.text.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(.ARM.exidx.text.xQueueCreateCountingSemaphore) refers to queue.o(.text.xQueueCreateCountingSemaphore) for [Anonymous Symbol]
    queue.o(.text.prvUnlockQueue) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.prvUnlockQueue) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.prvUnlockQueue) refers to tasks.o(.text.vTaskMissedYield) for vTaskMissedYield
    queue.o(.text.prvUnlockQueue) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.ARM.exidx.text.prvUnlockQueue) refers to queue.o(.text.prvUnlockQueue) for [Anonymous Symbol]
    queue.o(.text.xQueueGenericSendFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    queue.o(.text.xQueueGenericSendFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    queue.o(.text.xQueueGenericSendFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueueGenericSendFromISR) refers to tasks.o(.text.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(.text.xQueueGenericSendFromISR) refers to tasks.o(.text.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(.text.xQueueGenericSendFromISR) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.ARM.exidx.text.xQueueGenericSendFromISR) refers to queue.o(.text.xQueueGenericSendFromISR) for [Anonymous Symbol]
    queue.o(.text.xQueueGiveFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    queue.o(.text.xQueueGiveFromISR) refers to tasks.o(.text.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(.text.xQueueGiveFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    queue.o(.text.xQueueGiveFromISR) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.ARM.exidx.text.xQueueGiveFromISR) refers to queue.o(.text.xQueueGiveFromISR) for [Anonymous Symbol]
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(.text.xQueueReceive) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueReceive) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.xQueueReceive) refers to port.o(.text.vPortYield) for vPortYield
    queue.o(.text.xQueueReceive) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueueReceive) refers to queue.o(.text.prvUnlockQueue) for prvUnlockQueue
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(.ARM.exidx.text.xQueueReceive) refers to queue.o(.text.xQueueReceive) for [Anonymous Symbol]
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(.text.xQueuePeek) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueuePeek) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.xQueuePeek) refers to port.o(.text.vPortYield) for vPortYield
    queue.o(.text.xQueuePeek) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueuePeek) refers to queue.o(.text.prvUnlockQueue) for prvUnlockQueue
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(.ARM.exidx.text.xQueuePeek) refers to queue.o(.text.xQueuePeek) for [Anonymous Symbol]
    queue.o(.text.xQueueReceiveFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    queue.o(.text.xQueueReceiveFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueueReceiveFromISR) refers to tasks.o(.text.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(.text.xQueueReceiveFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    queue.o(.text.xQueueReceiveFromISR) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.ARM.exidx.text.xQueueReceiveFromISR) refers to queue.o(.text.xQueueReceiveFromISR) for [Anonymous Symbol]
    queue.o(.text.xQueuePeekFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    queue.o(.text.xQueuePeekFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueuePeekFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    queue.o(.ARM.exidx.text.xQueuePeekFromISR) refers to queue.o(.text.xQueuePeekFromISR) for [Anonymous Symbol]
    queue.o(.text.uxQueueMessagesWaiting) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.uxQueueMessagesWaiting) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.ARM.exidx.text.uxQueueMessagesWaiting) refers to queue.o(.text.uxQueueMessagesWaiting) for [Anonymous Symbol]
    queue.o(.text.uxQueueSpacesAvailable) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.uxQueueSpacesAvailable) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.ARM.exidx.text.uxQueueSpacesAvailable) refers to queue.o(.text.uxQueueSpacesAvailable) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.uxQueueMessagesWaitingFromISR) refers to queue.o(.text.uxQueueMessagesWaitingFromISR) for [Anonymous Symbol]
    queue.o(.text.vQueueDelete) refers to heap_4.o(.text.vPortFree) for vPortFree
    queue.o(.text.vQueueDelete) refers to queue.o(.bss.xQueueRegistry) for xQueueRegistry
    queue.o(.ARM.exidx.text.vQueueDelete) refers to queue.o(.text.vQueueDelete) for [Anonymous Symbol]
    queue.o(.text.vQueueUnregisterQueue) refers to queue.o(.bss.xQueueRegistry) for xQueueRegistry
    queue.o(.ARM.exidx.text.vQueueUnregisterQueue) refers to queue.o(.text.vQueueUnregisterQueue) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.uxQueueGetQueueNumber) refers to queue.o(.text.uxQueueGetQueueNumber) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.vQueueSetQueueNumber) refers to queue.o(.text.vQueueSetQueueNumber) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.ucQueueGetQueueType) refers to queue.o(.text.ucQueueGetQueueType) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.xQueueIsQueueEmptyFromISR) refers to queue.o(.text.xQueueIsQueueEmptyFromISR) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.xQueueIsQueueFullFromISR) refers to queue.o(.text.xQueueIsQueueFullFromISR) for [Anonymous Symbol]
    queue.o(.text.vQueueAddToRegistry) refers to queue.o(.bss.xQueueRegistry) for xQueueRegistry
    queue.o(.ARM.exidx.text.vQueueAddToRegistry) refers to queue.o(.text.vQueueAddToRegistry) for [Anonymous Symbol]
    queue.o(.text.pcQueueGetName) refers to queue.o(.bss.xQueueRegistry) for xQueueRegistry
    queue.o(.ARM.exidx.text.pcQueueGetName) refers to queue.o(.text.pcQueueGetName) for [Anonymous Symbol]
    queue.o(.text.vQueueWaitForMessageRestricted) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.vQueueWaitForMessageRestricted) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.vQueueWaitForMessageRestricted) refers to queue.o(.text.prvUnlockQueue) for prvUnlockQueue
    queue.o(.text.vQueueWaitForMessageRestricted) refers to tasks.o(.text.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(.ARM.exidx.text.vQueueWaitForMessageRestricted) refers to queue.o(.text.vQueueWaitForMessageRestricted) for [Anonymous Symbol]
    tasks.o(.text.xTaskCreate) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    tasks.o(.text.xTaskCreate) refers to memseta.o(.text) for __aeabi_memclr
    tasks.o(.text.xTaskCreate) refers to heap_4.o(.text.vPortFree) for vPortFree
    tasks.o(.text.xTaskCreate) refers to list.o(.text.vListInitialiseItem) for vListInitialiseItem
    tasks.o(.text.xTaskCreate) refers to port.o(.text.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(.text.xTaskCreate) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskCreate) refers to list.o(.text.vListInitialise) for vListInitialise
    tasks.o(.text.xTaskCreate) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskCreate) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.xTaskCreate) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskCreate) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.xTaskCreate) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskCreate) refers to tasks.o(.bss..L_MergedGlobals.1) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskCreate) refers to tasks.o(.text.xTaskCreate) for [Anonymous Symbol]
    tasks.o(.text.vTaskDelete) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskDelete) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskDelete) refers to list.o(.text.vListInsertEnd) for vListInsertEnd
    tasks.o(.text.vTaskDelete) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskDelete) refers to heap_4.o(.text.vPortFree) for vPortFree
    tasks.o(.text.vTaskDelete) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.vTaskDelete) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskDelete) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskDelete) refers to tasks.o(.text.vTaskDelete) for [Anonymous Symbol]
    tasks.o(.text.xTaskDelayUntil) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.xTaskDelayUntil) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.xTaskDelayUntil) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    tasks.o(.text.xTaskDelayUntil) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.xTaskDelayUntil) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskDelayUntil) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskDelayUntil) refers to tasks.o(.text.xTaskDelayUntil) for [Anonymous Symbol]
    tasks.o(.text.vTaskSuspendAll) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskSuspendAll) refers to tasks.o(.text.vTaskSuspendAll) for [Anonymous Symbol]
    tasks.o(.text.xTaskResumeAll) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskResumeAll) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskResumeAll) refers to tasks.o(.text.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(.text.xTaskResumeAll) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.xTaskResumeAll) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskResumeAll) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskResumeAll) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskResumeAll) refers to tasks.o(.text.xTaskResumeAll) for [Anonymous Symbol]
    tasks.o(.text.vTaskDelay) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskDelay) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.vTaskDelay) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    tasks.o(.text.vTaskDelay) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.vTaskDelay) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskDelay) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.vTaskDelay) refers to tasks.o(.text.vTaskDelay) for [Anonymous Symbol]
    tasks.o(.text.eTaskGetState) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.eTaskGetState) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.eTaskGetState) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.eTaskGetState) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.eTaskGetState) refers to tasks.o(.text.eTaskGetState) for [Anonymous Symbol]
    tasks.o(.text.uxTaskPriorityGet) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.uxTaskPriorityGet) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.uxTaskPriorityGet) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.uxTaskPriorityGet) refers to tasks.o(.text.uxTaskPriorityGet) for [Anonymous Symbol]
    tasks.o(.text.uxTaskPriorityGetFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    tasks.o(.text.uxTaskPriorityGetFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    tasks.o(.text.uxTaskPriorityGetFromISR) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.uxTaskPriorityGetFromISR) refers to tasks.o(.text.uxTaskPriorityGetFromISR) for [Anonymous Symbol]
    tasks.o(.text.vTaskPrioritySet) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskPrioritySet) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskPrioritySet) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskPrioritySet) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.vTaskPrioritySet) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskPrioritySet) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.vTaskPrioritySet) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskPrioritySet) refers to tasks.o(.text.vTaskPrioritySet) for [Anonymous Symbol]
    tasks.o(.text.vTaskSuspend) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskSuspend) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskSuspend) refers to list.o(.text.vListInsertEnd) for vListInsertEnd
    tasks.o(.text.vTaskSuspend) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskSuspend) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.vTaskSuspend) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskSuspend) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskSuspend) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskSuspend) refers to tasks.o(.text.vTaskSuspend) for [Anonymous Symbol]
    tasks.o(.text.vTaskSwitchContext) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskSwitchContext) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.vTaskSwitchContext) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.vTaskSwitchContext) refers to tasks.o(.text.vTaskSwitchContext) for [Anonymous Symbol]
    tasks.o(.text.vTaskResume) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskResume) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskResume) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.vTaskResume) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskResume) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskResume) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskResume) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskResume) refers to tasks.o(.text.vTaskResume) for [Anonymous Symbol]
    tasks.o(.text.xTaskResumeFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    tasks.o(.text.xTaskResumeFromISR) refers to list.o(.text.vListInsertEnd) for vListInsertEnd
    tasks.o(.text.xTaskResumeFromISR) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.xTaskResumeFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    tasks.o(.text.xTaskResumeFromISR) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskResumeFromISR) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.xTaskResumeFromISR) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskResumeFromISR) refers to tasks.o(.text.xTaskResumeFromISR) for [Anonymous Symbol]
    tasks.o(.text.vTaskStartScheduler) refers to tasks.o(.text.xTaskCreate) for xTaskCreate
    tasks.o(.text.vTaskStartScheduler) refers to timers.o(.text.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(.text.vTaskStartScheduler) refers to port.o(.text.xPortStartScheduler) for xPortStartScheduler
    tasks.o(.text.vTaskStartScheduler) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskStartScheduler) refers to tasks.o(.text.prvIdleTask) for prvIdleTask
    tasks.o(.text.vTaskStartScheduler) refers to tasks.o(.rodata.uxTopUsedPriority) for uxTopUsedPriority
    tasks.o(.ARM.exidx.text.vTaskStartScheduler) refers to tasks.o(.text.vTaskStartScheduler) for [Anonymous Symbol]
    tasks.o(.text.prvIdleTask) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.prvIdleTask) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.prvIdleTask) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.prvIdleTask) refers to heap_4.o(.text.vPortFree) for vPortFree
    tasks.o(.text.prvIdleTask) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.prvIdleTask) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.prvIdleTask) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.prvIdleTask) refers to tasks.o(.text.prvIdleTask) for [Anonymous Symbol]
    tasks.o(.text.vTaskEndScheduler) refers to port.o(.text.vPortEndScheduler) for vPortEndScheduler
    tasks.o(.text.vTaskEndScheduler) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskEndScheduler) refers to tasks.o(.text.vTaskEndScheduler) for [Anonymous Symbol]
    tasks.o(.text.xTaskIncrementTick) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskIncrementTick) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskIncrementTick) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskIncrementTick) refers to tasks.o(.text.xTaskIncrementTick) for [Anonymous Symbol]
    tasks.o(.text.xTaskGetTickCount) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskGetTickCount) refers to tasks.o(.text.xTaskGetTickCount) for [Anonymous Symbol]
    tasks.o(.text.xTaskGetTickCountFromISR) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskGetTickCountFromISR) refers to tasks.o(.text.xTaskGetTickCountFromISR) for [Anonymous Symbol]
    tasks.o(.text.uxTaskGetNumberOfTasks) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.uxTaskGetNumberOfTasks) refers to tasks.o(.text.uxTaskGetNumberOfTasks) for [Anonymous Symbol]
    tasks.o(.text.pcTaskGetName) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.pcTaskGetName) refers to tasks.o(.text.pcTaskGetName) for [Anonymous Symbol]
    tasks.o(.text.uxTaskGetSystemState) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    tasks.o(.text.uxTaskGetSystemState) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.uxTaskGetSystemState) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.uxTaskGetSystemState) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.uxTaskGetSystemState) refers to tasks.o(.text.uxTaskGetSystemState) for [Anonymous Symbol]
    tasks.o(.text.xTaskCatchUpTicks) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskCatchUpTicks) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskCatchUpTicks) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    tasks.o(.text.xTaskCatchUpTicks) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskCatchUpTicks) refers to tasks.o(.text.xTaskCatchUpTicks) for [Anonymous Symbol]
    tasks.o(.text.vTaskPlaceOnEventList) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.vTaskPlaceOnEventList) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskPlaceOnEventList) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskPlaceOnEventList) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskPlaceOnEventList) refers to tasks.o(.text.vTaskPlaceOnEventList) for [Anonymous Symbol]
    tasks.o(.text.vTaskPlaceOnUnorderedEventList) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskPlaceOnUnorderedEventList) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.text.vTaskPlaceOnUnorderedEventList) for [Anonymous Symbol]
    tasks.o(.text.vTaskPlaceOnEventListRestricted) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskPlaceOnEventListRestricted) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.vTaskPlaceOnEventListRestricted) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskPlaceOnEventListRestricted) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskPlaceOnEventListRestricted) refers to tasks.o(.text.vTaskPlaceOnEventListRestricted) for [Anonymous Symbol]
    tasks.o(.text.xTaskRemoveFromEventList) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskRemoveFromEventList) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskRemoveFromEventList) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskRemoveFromEventList) refers to tasks.o(.text.xTaskRemoveFromEventList) for [Anonymous Symbol]
    tasks.o(.text.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.text.vTaskRemoveFromUnorderedEventList) for [Anonymous Symbol]
    tasks.o(.text.vTaskSetTimeOutState) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskSetTimeOutState) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskSetTimeOutState) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskSetTimeOutState) refers to tasks.o(.text.vTaskSetTimeOutState) for [Anonymous Symbol]
    tasks.o(.text.vTaskInternalSetTimeOutState) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskInternalSetTimeOutState) refers to tasks.o(.text.vTaskInternalSetTimeOutState) for [Anonymous Symbol]
    tasks.o(.text.xTaskCheckForTimeOut) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskCheckForTimeOut) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskCheckForTimeOut) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskCheckForTimeOut) refers to tasks.o(.text.xTaskCheckForTimeOut) for [Anonymous Symbol]
    tasks.o(.text.vTaskMissedYield) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskMissedYield) refers to tasks.o(.text.vTaskMissedYield) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.uxTaskGetTaskNumber) refers to tasks.o(.text.uxTaskGetTaskNumber) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskSetTaskNumber) refers to tasks.o(.text.vTaskSetTaskNumber) for [Anonymous Symbol]
    tasks.o(.text.vTaskGetInfo) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskGetInfo) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskGetInfo) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    tasks.o(.text.vTaskGetInfo) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskGetInfo) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskGetInfo) refers to tasks.o(.text.vTaskGetInfo) for [Anonymous Symbol]
    tasks.o(.text.uxTaskGetStackHighWaterMark) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.uxTaskGetStackHighWaterMark) refers to tasks.o(.text.uxTaskGetStackHighWaterMark) for [Anonymous Symbol]
    tasks.o(.text.xTaskGetCurrentTaskHandle) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskGetCurrentTaskHandle) refers to tasks.o(.text.xTaskGetCurrentTaskHandle) for [Anonymous Symbol]
    tasks.o(.text.xTaskGetSchedulerState) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskGetSchedulerState) refers to tasks.o(.text.xTaskGetSchedulerState) for [Anonymous Symbol]
    tasks.o(.text.xTaskPriorityInherit) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.xTaskPriorityInherit) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.xTaskPriorityInherit) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskPriorityInherit) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskPriorityInherit) refers to tasks.o(.text.xTaskPriorityInherit) for [Anonymous Symbol]
    tasks.o(.text.xTaskPriorityDisinherit) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.xTaskPriorityDisinherit) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.xTaskPriorityDisinherit) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskPriorityDisinherit) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskPriorityDisinherit) refers to tasks.o(.text.xTaskPriorityDisinherit) for [Anonymous Symbol]
    tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) for [Anonymous Symbol]
    tasks.o(.text.uxTaskResetEventItemValue) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.uxTaskResetEventItemValue) refers to tasks.o(.text.uxTaskResetEventItemValue) for [Anonymous Symbol]
    tasks.o(.text.pvTaskIncrementMutexHeldCount) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.pvTaskIncrementMutexHeldCount) refers to tasks.o(.text.pvTaskIncrementMutexHeldCount) for [Anonymous Symbol]
    tasks.o(.text.ulTaskGenericNotifyTake) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.ulTaskGenericNotifyTake) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.ulTaskGenericNotifyTake) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.ulTaskGenericNotifyTake) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.ulTaskGenericNotifyTake) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.ulTaskGenericNotifyTake) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.ulTaskGenericNotifyTake) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.ulTaskGenericNotifyTake) refers to tasks.o(.text.ulTaskGenericNotifyTake) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotifyWait) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskGenericNotifyWait) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.xTaskGenericNotifyWait) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.xTaskGenericNotifyWait) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.xTaskGenericNotifyWait) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskGenericNotifyWait) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.xTaskGenericNotifyWait) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskGenericNotifyWait) refers to tasks.o(.text.xTaskGenericNotifyWait) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotify) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskGenericNotify) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskGenericNotify) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.xTaskGenericNotify) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotify) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotify) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskGenericNotify) refers to tasks.o(.text.xTaskGenericNotify) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotifyFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    tasks.o(.text.xTaskGenericNotifyFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    tasks.o(.text.xTaskGenericNotifyFromISR) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotifyFromISR) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotifyFromISR) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskGenericNotifyFromISR) refers to tasks.o(.text.xTaskGenericNotifyFromISR) for [Anonymous Symbol]
    tasks.o(.text.vTaskGenericNotifyGiveFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    tasks.o(.text.vTaskGenericNotifyGiveFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    tasks.o(.text.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.text.vTaskGenericNotifyGiveFromISR) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotifyStateClear) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskGenericNotifyStateClear) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskGenericNotifyStateClear) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskGenericNotifyStateClear) refers to tasks.o(.text.xTaskGenericNotifyStateClear) for [Anonymous Symbol]
    tasks.o(.text.ulTaskGenericNotifyValueClear) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.ulTaskGenericNotifyValueClear) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.ulTaskGenericNotifyValueClear) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.ulTaskGenericNotifyValueClear) refers to tasks.o(.text.ulTaskGenericNotifyValueClear) for [Anonymous Symbol]
    timers.o(.text.xTimerCreateTimerTask) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.xTimerCreateTimerTask) refers to list.o(.text.vListInitialise) for vListInitialise
    timers.o(.text.xTimerCreateTimerTask) refers to queue.o(.text.xQueueGenericCreate) for xQueueGenericCreate
    timers.o(.text.xTimerCreateTimerTask) refers to queue.o(.text.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(.text.xTimerCreateTimerTask) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.text.xTimerCreateTimerTask) refers to tasks.o(.text.xTaskCreate) for xTaskCreate
    timers.o(.text.xTimerCreateTimerTask) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.text.xTimerCreateTimerTask) refers to timers.o(.rodata.str1.1) for [Anonymous Symbol]
    timers.o(.text.xTimerCreateTimerTask) refers to timers.o(.text.prvTimerTask) for prvTimerTask
    timers.o(.ARM.exidx.text.xTimerCreateTimerTask) refers to timers.o(.text.xTimerCreateTimerTask) for [Anonymous Symbol]
    timers.o(.text.prvTimerTask) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(.text.prvTimerTask) refers to tasks.o(.text.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(.text.prvTimerTask) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    timers.o(.text.prvTimerTask) refers to list.o(.text.uxListRemove) for uxListRemove
    timers.o(.text.prvTimerTask) refers to list.o(.text.vListInsert) for vListInsert
    timers.o(.text.prvTimerTask) refers to queue.o(.text.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(.text.prvTimerTask) refers to port.o(.text.vPortYield) for vPortYield
    timers.o(.text.prvTimerTask) refers to queue.o(.text.xQueueReceive) for xQueueReceive
    timers.o(.text.prvTimerTask) refers to heap_4.o(.text.vPortFree) for vPortFree
    timers.o(.text.prvTimerTask) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.prvTimerTask) refers to timers.o(.text.prvTimerTask) for [Anonymous Symbol]
    timers.o(.text.xTimerCreate) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    timers.o(.text.xTimerCreate) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.xTimerCreate) refers to list.o(.text.vListInitialise) for vListInitialise
    timers.o(.text.xTimerCreate) refers to queue.o(.text.xQueueGenericCreate) for xQueueGenericCreate
    timers.o(.text.xTimerCreate) refers to queue.o(.text.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(.text.xTimerCreate) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.text.xTimerCreate) refers to list.o(.text.vListInitialiseItem) for vListInitialiseItem
    timers.o(.text.xTimerCreate) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.text.xTimerCreate) refers to timers.o(.rodata.str1.1) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerCreate) refers to timers.o(.text.xTimerCreate) for [Anonymous Symbol]
    timers.o(.text.xTimerGenericCommand) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(.text.xTimerGenericCommand) refers to queue.o(.text.xQueueGenericSend) for xQueueGenericSend
    timers.o(.text.xTimerGenericCommand) refers to queue.o(.text.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(.text.xTimerGenericCommand) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerGenericCommand) refers to timers.o(.text.xTimerGenericCommand) for [Anonymous Symbol]
    timers.o(.text.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.text.xTimerGetTimerDaemonTaskHandle) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerGetPeriod) refers to timers.o(.text.xTimerGetPeriod) for [Anonymous Symbol]
    timers.o(.text.vTimerSetReloadMode) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.vTimerSetReloadMode) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.vTimerSetReloadMode) refers to timers.o(.text.vTimerSetReloadMode) for [Anonymous Symbol]
    timers.o(.text.xTimerGetReloadMode) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.xTimerGetReloadMode) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.xTimerGetReloadMode) refers to timers.o(.text.xTimerGetReloadMode) for [Anonymous Symbol]
    timers.o(.text.uxTimerGetReloadMode) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.uxTimerGetReloadMode) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.uxTimerGetReloadMode) refers to timers.o(.text.uxTimerGetReloadMode) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerGetExpiryTime) refers to timers.o(.text.xTimerGetExpiryTime) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.pcTimerGetName) refers to timers.o(.text.pcTimerGetName) for [Anonymous Symbol]
    timers.o(.text.xTimerIsTimerActive) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.xTimerIsTimerActive) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.xTimerIsTimerActive) refers to timers.o(.text.xTimerIsTimerActive) for [Anonymous Symbol]
    timers.o(.text.pvTimerGetTimerID) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.pvTimerGetTimerID) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.pvTimerGetTimerID) refers to timers.o(.text.pvTimerGetTimerID) for [Anonymous Symbol]
    timers.o(.text.vTimerSetTimerID) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.vTimerSetTimerID) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.vTimerSetTimerID) refers to timers.o(.text.vTimerSetTimerID) for [Anonymous Symbol]
    timers.o(.text.xTimerPendFunctionCallFromISR) refers to queue.o(.text.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(.text.xTimerPendFunctionCallFromISR) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerPendFunctionCallFromISR) refers to timers.o(.text.xTimerPendFunctionCallFromISR) for [Anonymous Symbol]
    timers.o(.text.xTimerPendFunctionCall) refers to queue.o(.text.xQueueGenericSend) for xQueueGenericSend
    timers.o(.text.xTimerPendFunctionCall) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerPendFunctionCall) refers to timers.o(.text.xTimerPendFunctionCall) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.uxTimerGetTimerNumber) refers to timers.o(.text.uxTimerGetTimerNumber) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.vTimerSetTimerNumber) refers to timers.o(.text.vTimerSetTimerNumber) for [Anonymous Symbol]
    heap_4.o(.text.pvPortMalloc) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(.text.pvPortMalloc) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(.text.pvPortMalloc) refers to heap_4.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    heap_4.o(.text.pvPortMalloc) refers to heap_4.o(.bss.ucHeap) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.pvPortMalloc) refers to heap_4.o(.text.pvPortMalloc) for [Anonymous Symbol]
    heap_4.o(.text.vPortFree) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(.text.vPortFree) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(.text.vPortFree) refers to heap_4.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.vPortFree) refers to heap_4.o(.text.vPortFree) for [Anonymous Symbol]
    heap_4.o(.text.xPortGetFreeHeapSize) refers to heap_4.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.xPortGetFreeHeapSize) refers to heap_4.o(.text.xPortGetFreeHeapSize) for [Anonymous Symbol]
    heap_4.o(.text.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.text.xPortGetMinimumEverFreeHeapSize) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.vPortInitialiseBlocks) refers to heap_4.o(.text.vPortInitialiseBlocks) for [Anonymous Symbol]
    heap_4.o(.text.pvPortCalloc) refers to llmul.o(.text) for __aeabi_lmul
    heap_4.o(.text.pvPortCalloc) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    heap_4.o(.text.pvPortCalloc) refers to memseta.o(.text) for __aeabi_memclr
    heap_4.o(.ARM.exidx.text.pvPortCalloc) refers to heap_4.o(.text.pvPortCalloc) for [Anonymous Symbol]
    heap_4.o(.text.vPortGetHeapStats) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(.text.vPortGetHeapStats) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(.text.vPortGetHeapStats) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(.text.vPortGetHeapStats) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    heap_4.o(.text.vPortGetHeapStats) refers to heap_4.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.vPortGetHeapStats) refers to heap_4.o(.text.vPortGetHeapStats) for [Anonymous Symbol]
    port.o(.text.pxPortInitialiseStack) refers to port.o(.text.prvTaskExitError) for prvTaskExitError
    port.o(.ARM.exidx.text.pxPortInitialiseStack) refers to port.o(.text.pxPortInitialiseStack) for [Anonymous Symbol]
    port.o(.text.prvTaskExitError) refers to port.o(.data.uxCriticalNesting) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.prvTaskExitError) refers to port.o(.text.prvTaskExitError) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.SVC_Handler) refers to port.o(.text.SVC_Handler) for [Anonymous Symbol]
    port.o(.text.xPortStartScheduler) refers to port.o(.text.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(.text.xPortStartScheduler) refers to port.o(.text.vPortStartFirstTask) for vPortStartFirstTask
    port.o(.text.xPortStartScheduler) refers to tasks.o(.text.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.text.xPortStartScheduler) refers to port.o(.data.uxCriticalNesting) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.xPortStartScheduler) refers to port.o(.text.xPortStartScheduler) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vPortSetupTimerInterrupt) refers to port.o(.text.vPortSetupTimerInterrupt) for [Anonymous Symbol]
    port.o(.text.vPortStartFirstTask) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    port.o(.ARM.exidx.text.vPortStartFirstTask) refers to port.o(.text.vPortStartFirstTask) for [Anonymous Symbol]
    port.o(.text.vPortEndScheduler) refers to port.o(.data.uxCriticalNesting) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vPortEndScheduler) refers to port.o(.text.vPortEndScheduler) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vPortYield) refers to port.o(.text.vPortYield) for [Anonymous Symbol]
    port.o(.text.vPortEnterCritical) refers to port.o(.data.uxCriticalNesting) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vPortEnterCritical) refers to port.o(.text.vPortEnterCritical) for [Anonymous Symbol]
    port.o(.text.vPortExitCritical) refers to port.o(.data.uxCriticalNesting) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vPortExitCritical) refers to port.o(.text.vPortExitCritical) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.ulSetInterruptMaskFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vClearInterruptMaskFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for [Anonymous Symbol]
    port.o(.text.PendSV_Handler) refers to tasks.o(.text.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.text.PendSV_Handler) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    port.o(.ARM.exidx.text.PendSV_Handler) refers to port.o(.text.PendSV_Handler) for [Anonymous Symbol]
    port.o(.text.SysTick_Handler) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    port.o(.text.SysTick_Handler) refers to tasks.o(.text.xTaskIncrementTick) for xTaskIncrementTick
    port.o(.text.SysTick_Handler) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    port.o(.ARM.exidx.text.SysTick_Handler) refers to port.o(.text.SysTick_Handler) for [Anonymous Symbol]
    startuptask.o(.text.StartupTask) refers to tasks.o(.text.xTaskCreate) for xTaskCreate
    startuptask.o(.text.StartupTask) refers to startuptask.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    startuptask.o(.text.StartupTask) refers to startuptask.o(.text.RGB_task) for RGB_task
    startuptask.o(.text.StartupTask) refers to startuptask.o(.text.OLED_task) for OLED_task
    startuptask.o(.text.StartupTask) refers to startuptask.o(.text.main_task) for main_task
    startuptask.o(.text.StartupTask) refers to startuptask.o(.text.line_task) for line_task
    startuptask.o(.ARM.exidx.text.StartupTask) refers to startuptask.o(.text.StartupTask) for [Anonymous Symbol]
    startuptask.o(.text.RGB_task) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    startuptask.o(.ARM.exidx.text.RGB_task) refers to startuptask.o(.text.RGB_task) for [Anonymous Symbol]
    startuptask.o(.text.OLED_task) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    startuptask.o(.text.OLED_task) refers to enconder.o(.text.Get_Encoder) for Get_Encoder
    startuptask.o(.text.OLED_task) refers to oled.o(.text.OLED_Write) for OLED_Write
    startuptask.o(.text.OLED_task) refers to motor.o(.text.Motor_Write) for Motor_Write
    startuptask.o(.text.OLED_task) refers to startuptask.o(.bss.distance) for distance
    startuptask.o(.ARM.exidx.text.OLED_task) refers to startuptask.o(.text.OLED_task) for [Anonymous Symbol]
    startuptask.o(.text.main_task) refers to control.o(.text.PID_Init) for PID_Init
    startuptask.o(.text.main_task) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    startuptask.o(.text.main_task) refers to oled.o(.text.OLED_Write) for OLED_Write
    startuptask.o(.text.main_task) refers to emm_v5.o(.text.Emm_V5_En_Control) for Emm_V5_En_Control
    startuptask.o(.text.main_task) refers to startuptask.o(.bss.Start_flag) for Start_flag
    startuptask.o(.ARM.exidx.text.main_task) refers to startuptask.o(.text.main_task) for [Anonymous Symbol]
    startuptask.o(.text.line_task) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    startuptask.o(.text.line_task) refers to control.o(.text.Line_Control) for Line_Control
    startuptask.o(.text.line_task) refers to startuptask.o(.data.Line_EN) for Line_EN
    startuptask.o(.ARM.exidx.text.line_task) refers to startuptask.o(.text.line_task) for [Anonymous Symbol]
    startuptask.o(.ARM.exidx.text.vApplicationStackOverflowHook) refers to startuptask.o(.text.vApplicationStackOverflowHook) for [Anonymous Symbol]
    startuptask.o(.ARM.exidx.text.vApplicationMallocFailedHook) refers to startuptask.o(.text.vApplicationMallocFailedHook) for [Anonymous Symbol]
    startuptask.o(.ARM.exidx.text.vApplicationIdleHook) refers to startuptask.o(.text.vApplicationIdleHook) for [Anonymous Symbol]
    startuptask.o(.ARM.exidx.text.vApplicationTickHook) refers to startuptask.o(.text.vApplicationTickHook) for [Anonymous Symbol]
    emm_v5.o(.ARM.exidx.text.ZDT_Init) refers to emm_v5.o(.text.ZDT_Init) for [Anonymous Symbol]
    emm_v5.o(.text.select_ZDT) refers to emm_v5.o(.bss.ZDT_Switch) for ZDT_Switch
    emm_v5.o(.ARM.exidx.text.select_ZDT) refers to emm_v5.o(.text.select_ZDT) for [Anonymous Symbol]
    emm_v5.o(.text.ZDT_SetSpeed) refers to emm_v5.o(.bss.ZDT_Switch) for ZDT_Switch
    emm_v5.o(.ARM.exidx.text.ZDT_SetSpeed) refers to emm_v5.o(.text.ZDT_SetSpeed) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Vel_Control) refers to emm_v5.o(.bss.ZDT_Switch) for ZDT_Switch
    emm_v5.o(.ARM.exidx.text.Emm_V5_Vel_Control) refers to emm_v5.o(.text.Emm_V5_Vel_Control) for [Anonymous Symbol]
    emm_v5.o(.ARM.exidx.text.usart_SendByte) refers to emm_v5.o(.text.usart_SendByte) for [Anonymous Symbol]
    emm_v5.o(.ARM.exidx.text.usart_SendCmd) refers to emm_v5.o(.text.usart_SendCmd) for [Anonymous Symbol]
    emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_CurPos_To_Zero) refers to emm_v5.o(.text.Emm_V5_Reset_CurPos_To_Zero) for [Anonymous Symbol]
    emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_Clog_Pro) refers to emm_v5.o(.text.Emm_V5_Reset_Clog_Pro) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Read_Sys_Params) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.ARM.exidx.text.Emm_V5_Read_Sys_Params) refers to emm_v5.o(.text.Emm_V5_Read_Sys_Params) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Modify_Ctrl_Mode) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.ARM.exidx.text.Emm_V5_Modify_Ctrl_Mode) refers to emm_v5.o(.text.Emm_V5_Modify_Ctrl_Mode) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_En_Control) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.ARM.exidx.text.Emm_V5_En_Control) refers to emm_v5.o(.text.Emm_V5_En_Control) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Pos_Control) refers to emm_v5.o(.bss.ZDT_Switch) for ZDT_Switch
    emm_v5.o(.ARM.exidx.text.Emm_V5_Pos_Control) refers to emm_v5.o(.text.Emm_V5_Pos_Control) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Stop_Now) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.ARM.exidx.text.Emm_V5_Stop_Now) refers to emm_v5.o(.text.Emm_V5_Stop_Now) for [Anonymous Symbol]
    emm_v5.o(.ARM.exidx.text.Emm_V5_Synchronous_motion) refers to emm_v5.o(.text.Emm_V5_Synchronous_motion) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Origin_Set_O) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Set_O) refers to emm_v5.o(.text.Emm_V5_Origin_Set_O) for [Anonymous Symbol]
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Modify_Params) refers to emm_v5.o(.text.Emm_V5_Origin_Modify_Params) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Origin_Trigger_Return) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Trigger_Return) refers to emm_v5.o(.text.Emm_V5_Origin_Trigger_Return) for [Anonymous Symbol]
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Interrupt) refers to emm_v5.o(.text.Emm_V5_Origin_Interrupt) for [Anonymous Symbol]
    ws2812b.o(.ARM.exidx.text.WS2812_RESET) refers to ws2812b.o(.text.WS2812_RESET) for [Anonymous Symbol]
    ws2812b.o(.text.WS2812_Set_Color) refers to ws2812b.o(.bss.LedsArray) for [Anonymous Symbol]
    ws2812b.o(.ARM.exidx.text.WS2812_Set_Color) refers to ws2812b.o(.text.WS2812_Set_Color) for [Anonymous Symbol]
    ws2812b.o(.text.WS2812_Send_Array) refers to ws2812b.o(.text.WS2812_RESET) for WS2812_RESET
    ws2812b.o(.text.WS2812_Send_Array) refers to ws2812b.o(.bss.LedsArray) for [Anonymous Symbol]
    ws2812b.o(.ARM.exidx.text.WS2812_Send_Array) refers to ws2812b.o(.text.WS2812_Send_Array) for [Anonymous Symbol]
    servo.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue2) refers to servo.o(.text.DL_Timer_setCaptureCompareValue2) for [Anonymous Symbol]
    servo.o(.text.Set_Servo) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    servo.o(.text.Set_Servo) refers to fdiv.o(.text) for __aeabi_fdiv
    servo.o(.text.Set_Servo) refers to fmul.o(.text) for __aeabi_fmul
    servo.o(.text.Set_Servo) refers to fadd.o(.text) for __aeabi_fadd
    servo.o(.text.Set_Servo) refers to ffixi.o(.text) for __aeabi_f2iz
    servo.o(.text.Set_Servo) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    servo.o(.ARM.exidx.text.Set_Servo) refers to servo.o(.text.Set_Servo) for [Anonymous Symbol]
    servo.o(.text.Set_Servo_raw) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    servo.o(.ARM.exidx.text.Set_Servo_raw) refers to servo.o(.text.Set_Servo_raw) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.delay) refers to oled.o(.text.delay) for [Anonymous Symbol]
    oled.o(.text.IIC_Start) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.IIC_Start) refers to oled.o(.text.IIC_Start) for [Anonymous Symbol]
    oled.o(.text.IIC_Stop) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.IIC_Stop) refers to oled.o(.text.IIC_Stop) for [Anonymous Symbol]
    oled.o(.text.IIC_Ack) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.IIC_Ack) refers to oled.o(.text.IIC_Ack) for [Anonymous Symbol]
    oled.o(.text.IIC_XIE) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.IIC_XIE) refers to oled.o(.text.IIC_XIE) for [Anonymous Symbol]
    oled.o(.text.IIC_XIE_ML) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.IIC_XIE_ML) refers to oled.o(.text.IIC_XIE) for IIC_XIE
    oled.o(.ARM.exidx.text.IIC_XIE_ML) refers to oled.o(.text.IIC_XIE_ML) for [Anonymous Symbol]
    oled.o(.text.IIC_XIE_DATA) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.IIC_XIE_DATA) refers to oled.o(.text.IIC_XIE) for IIC_XIE
    oled.o(.ARM.exidx.text.IIC_XIE_DATA) refers to oled.o(.text.IIC_XIE_DATA) for [Anonymous Symbol]
    oled.o(.text.OLED_Set_Pos) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.ARM.exidx.text.OLED_Set_Pos) refers to oled.o(.text.OLED_Set_Pos) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.text.OLED_Clear) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_Clear) refers to oled.o(.text.IIC_XIE) for IIC_XIE
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.IIC_XIE_DATA) for IIC_XIE_DATA
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.F6x8) for F6x8
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.F8X16) for F8X16
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChinese) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.text.OLED_ShowChinese) refers to oled.o(.text.IIC_XIE_DATA) for IIC_XIE_DATA
    oled.o(.text.OLED_ShowChinese) refers to oled.o(.data.Hzk2) for Hzk2
    oled.o(.ARM.exidx.text.OLED_ShowChinese) refers to oled.o(.text.OLED_ShowChinese) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawBMP) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.text.OLED_DrawBMP) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_DrawBMP) refers to oled.o(.text.IIC_XIE) for IIC_XIE
    oled.o(.ARM.exidx.text.OLED_DrawBMP) refers to oled.o(.text.OLED_DrawBMP) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_IIC_Init) refers to oled.o(.text.OLED_IIC_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_Write) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    oled.o(.text.OLED_Write) refers to printfa.o(i.__0vsprintf) for vsprintf
    oled.o(.text.OLED_Write) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.text.OLED_Write) refers to oled.o(.data.OLED_Lock) for OLED_Lock
    oled.o(.ARM.exidx.text.OLED_Write) refers to oled.o(.text.OLED_Write) for [Anonymous Symbol]
    motor.o(.text.Set_Motor) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.ARM.exidx.text.Set_Motor) refers to motor.o(.text.Set_Motor) for [Anonymous Symbol]
    motor.o(.text.Motor_Write) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.ARM.exidx.text.Motor_Write) refers to motor.o(.text.Motor_Write) for [Anonymous Symbol]
    led_key.o(.ARM.exidx.text.GPIO_WriteBit) refers to led_key.o(.text.GPIO_WriteBit) for [Anonymous Symbol]
    enconder.o(.text.Get_Encoder) refers to enconder.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    enconder.o(.ARM.exidx.text.Get_Encoder) refers to enconder.o(.text.Get_Encoder) for [Anonymous Symbol]
    enconder.o(.ARM.exidx.text.Encoder_init) refers to enconder.o(.text.Encoder_init) for [Anonymous Symbol]
    enconder.o(.text.GROUP1_IRQHandler) refers to enconder.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    enconder.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to enconder.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    delay.o(.ARM.exidx.text.delay_noOS) refers to delay.o(.text.delay_noOS) for [Anonymous Symbol]
    control.o(.text.PID_Init) refers to control.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.PID_Init) refers to control.o(.text.PID_Init) for [Anonymous Symbol]
    control.o(.text.Erect_pid) refers to fadd.o(.text) for __aeabi_fsub
    control.o(.text.Erect_pid) refers to fmul.o(.text) for __aeabi_fmul
    control.o(.ARM.exidx.text.Erect_pid) refers to control.o(.text.Erect_pid) for [Anonymous Symbol]
    control.o(.text.Get_hw) refers to control.o(.bss.Line) for Line
    control.o(.ARM.exidx.text.Get_hw) refers to control.o(.text.Get_hw) for [Anonymous Symbol]
    control.o(.text.Line_Control) refers to idiv_div0.o(.text) for __aeabi_idiv
    control.o(.text.Line_Control) refers to motor.o(.text.Motor_Write) for Motor_Write
    control.o(.text.Line_Control) refers to control.o(.bss.Line) for Line
    control.o(.ARM.exidx.text.Line_Control) refers to control.o(.text.Line_Control) for [Anonymous Symbol]
    control.o(.text.Line_Control_Simple) refers to motor.o(.text.Motor_Write) for Motor_Write
    control.o(.text.Line_Control_Simple) refers to control.o(.bss.Line) for Line
    control.o(.ARM.exidx.text.Line_Control_Simple) refers to control.o(.text.Line_Control_Simple) for [Anonymous Symbol]
    control.o(.text.Motor_Open) refers to control.o(.text.Line_Control) for Line_Control
    control.o(.text.Motor_Open) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    control.o(.text.Motor_Open) refers to motor.o(.text.Motor_Write) for Motor_Write
    control.o(.text.Motor_Open) refers to control.o(.bss.Line) for Line
    control.o(.ARM.exidx.text.Motor_Open) refers to control.o(.text.Motor_Open) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.UART2_Init) refers to maixcam.o(.text.UART2_Init) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.uart2_send_char) refers to maixcam.o(.text.uart2_send_char) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.uart2_send_string) refers to maixcam.o(.text.uart2_send_string) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.fputc) refers to maixcam.o(.text.fputc) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.MaixCam_Init) refers to maixcam.o(.text.MaixCam_Init) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.MaixCam_Send_XY) refers to maixcam.o(.text.MaixCam_Send_XY) for [Anonymous Symbol]
    maixcam.o(.text.UART1_IRQHandler) refers to maixcam.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.UART1_IRQHandler) refers to maixcam.o(.text.UART1_IRQHandler) for [Anonymous Symbol]
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    idiv.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to maixcam.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to maixcam.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to maixcam.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to maixcam.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    fcmpgt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_setClockConfig), (64 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (40 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_aes.o(.text), (0 bytes).
    Removing dl_aes.o(.text.DL_AES_setKey), (72 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKey), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_setKeyAligned), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOut), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOutAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorData), (52 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorData), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorDataAligned), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_saveConfiguration), (64 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_restoreConfiguration), (60 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration), (8 bytes).
    Removing dl_aesadv.o(.text), (0 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_crc.o(.text), (0 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock32), (96 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange32), (52 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock16), (156 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange16), (104 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16), (8 bytes).
    Removing dl_crcp.o(.text), (0 bytes).
    Removing dl_dac12.o(.text), (0 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_init), (136 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_init), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking8), (48 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO8), (132 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking), (36 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.text.DL_DMA_initChannel), (68 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_flashctl.o(.text), (0 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemory), (22 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.ramfunc), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.ramfunc), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massErase), (124 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory), (16 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank), (332 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM), (264 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank), (544 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryReset), (192 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory), (12 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM), (116 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank), (92 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (144 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectSector), (224 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (104 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (176 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (132 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking), (168 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM), (120 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectSector), (240 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerify), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.text.DL_I2C_setClockConfig), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (152 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (68 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (156 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (68 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_keystorectl.o(.text), (0 bytes).
    Removing dl_lcd.o(.text), (0 bytes).
    Removing dl_lfss.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_configOperation), (40 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation), (8 bytes).
    Removing dl_mcan.o(.text), (0 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isReady), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setClockConfig), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockConfig), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isInReset), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isFDOpEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isMemInitDone), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setOpMode), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getOpMode), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_init), (216 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_init), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_config), (212 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_config), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccConfig), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setBitTime), (216 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_msgRAMConfig), (520 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setExtIDAndMask), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsgRam), (264 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufAddReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsgRam), (292 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readTxEventFIFO), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_lpbkModeEnable), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getErrCounters), (40 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getProtocolStatus), (80 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntr), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_selectIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearIntrStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus), (60 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufReqPend), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addClockStopRequest), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccForceError), (220 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus), (128 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus), (112 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWriteEOI), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccEnableIntr), (56 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterConfig), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterEnable), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSEnableIntr), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSWriteEOI), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRevisionId), (68 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setTxPinState), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTSCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClkStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getBitTime), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_resetTSCounter), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTOCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getEndianVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getExtIDANDMask), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_saveConfiguration), (236 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_restoreConfiguration), (276 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration), (8 bytes).
    Removing dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize), (64 bytes).
    Removing dl_mcan.o(.rodata.cst32), (32 bytes).
    Removing dl_opa.o(.text), (0 bytes).
    Removing dl_opa.o(.text.DL_OPA_increaseGain), (52 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_decreaseGain), (48 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain), (8 bytes).
    Removing dl_rtc_common.o(.text), (0 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_initCalendar), (144 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime), (124 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1), (96 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1), (84 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1), (60 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2), (96 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2), (84 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2), (60 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (16 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (100 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (100 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (88 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (112 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (268 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (104 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (224 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (160 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (96 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (200 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (196 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (272 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_trng.o(.text), (0 bytes).
    Removing dl_trng.o(.text.DL_TRNG_saveConfiguration), (52 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_restoreConfiguration), (72 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (68 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (48 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (36 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (100 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (100 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (96 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (112 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_vref.o(.text), (0 bytes).
    Removing dl_vref.o(.text.DL_VREF_configReference), (32 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_configReference), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_setClockConfig), (18 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_getClockConfig), (16 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig), (8 bytes).
    Removing dl_interrupt.o(.text), (0 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_registerInterrupt), (284 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt), (8 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt), (20 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt), (8 bytes).
    Removing dl_interrupt.o(.vtable), (192 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL), (240 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (100 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (96 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (108 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_Servo_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ZDT1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_WS2812_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (60 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (68 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing startup_mspm0g350x_uvision.o(HEAP), (512 bytes).
    Removing croutine.o(.text), (0 bytes).
    Removing event_groups.o(.text), (0 bytes).
    Removing event_groups.o(.text.xEventGroupCreate), (28 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupCreate), (8 bytes).
    Removing event_groups.o(.text.xEventGroupSync), (316 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupSync), (8 bytes).
    Removing event_groups.o(.text.xEventGroupSetBits), (140 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupSetBits), (8 bytes).
    Removing event_groups.o(.text.xEventGroupWaitBits), (236 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupWaitBits), (8 bytes).
    Removing event_groups.o(.text.xEventGroupClearBits), (42 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupClearBits), (8 bytes).
    Removing event_groups.o(.text.xEventGroupClearBitsFromISR), (20 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupClearBitsFromISR), (8 bytes).
    Removing event_groups.o(.text.vEventGroupClearBitsCallback), (38 bytes).
    Removing event_groups.o(.ARM.exidx.text.vEventGroupClearBitsCallback), (8 bytes).
    Removing event_groups.o(.text.xEventGroupGetBitsFromISR), (18 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupGetBitsFromISR), (8 bytes).
    Removing event_groups.o(.text.vEventGroupDelete), (62 bytes).
    Removing event_groups.o(.ARM.exidx.text.vEventGroupDelete), (8 bytes).
    Removing event_groups.o(.text.vEventGroupSetBitsCallback), (140 bytes).
    Removing event_groups.o(.ARM.exidx.text.vEventGroupSetBitsCallback), (8 bytes).
    Removing event_groups.o(.text.xEventGroupSetBitsFromISR), (20 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupSetBitsFromISR), (8 bytes).
    Removing event_groups.o(.text.uxEventGroupGetNumber), (12 bytes).
    Removing event_groups.o(.ARM.exidx.text.uxEventGroupGetNumber), (8 bytes).
    Removing event_groups.o(.text.vEventGroupSetNumber), (4 bytes).
    Removing event_groups.o(.ARM.exidx.text.vEventGroupSetNumber), (8 bytes).
    Removing list.o(.text), (0 bytes).
    Removing list.o(.ARM.exidx.text.vListInitialise), (8 bytes).
    Removing list.o(.ARM.exidx.text.vListInitialiseItem), (8 bytes).
    Removing list.o(.text.vListInsertEnd), (24 bytes).
    Removing list.o(.ARM.exidx.text.vListInsertEnd), (8 bytes).
    Removing list.o(.ARM.exidx.text.vListInsert), (8 bytes).
    Removing list.o(.ARM.exidx.text.uxListRemove), (8 bytes).
    Removing queue.o(.text), (0 bytes).
    Removing queue.o(.text.xQueueGenericReset), (140 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGenericReset), (8 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGenericCreate), (8 bytes).
    Removing queue.o(.text.xQueueCreateMutex), (112 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueCreateMutex), (8 bytes).
    Removing queue.o(.text.xQueueGetMutexHolder), (42 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGetMutexHolder), (8 bytes).
    Removing queue.o(.text.xQueueGetMutexHolderFromISR), (22 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGetMutexHolderFromISR), (8 bytes).
    Removing queue.o(.text.xQueueGiveMutexRecursive), (58 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGiveMutexRecursive), (8 bytes).
    Removing queue.o(.text.xQueueGenericSend), (416 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGenericSend), (8 bytes).
    Removing queue.o(.text.xQueueTakeMutexRecursive), (58 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueTakeMutexRecursive), (8 bytes).
    Removing queue.o(.text.xQueueSemaphoreTake), (360 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueSemaphoreTake), (8 bytes).
    Removing queue.o(.text.xQueueCreateCountingSemaphore), (32 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueCreateCountingSemaphore), (8 bytes).
    Removing queue.o(.ARM.exidx.text.prvUnlockQueue), (8 bytes).
    Removing queue.o(.text.xQueueGenericSendFromISR), (300 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGenericSendFromISR), (8 bytes).
    Removing queue.o(.text.xQueueGiveFromISR), (174 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGiveFromISR), (8 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueReceive), (8 bytes).
    Removing queue.o(.text.xQueuePeek), (320 bytes).
    Removing queue.o(.ARM.exidx.text.xQueuePeek), (8 bytes).
    Removing queue.o(.text.xQueueReceiveFromISR), (182 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueReceiveFromISR), (8 bytes).
    Removing queue.o(.text.xQueuePeekFromISR), (98 bytes).
    Removing queue.o(.ARM.exidx.text.xQueuePeekFromISR), (8 bytes).
    Removing queue.o(.text.uxQueueMessagesWaiting), (26 bytes).
    Removing queue.o(.ARM.exidx.text.uxQueueMessagesWaiting), (8 bytes).
    Removing queue.o(.text.uxQueueSpacesAvailable), (30 bytes).
    Removing queue.o(.ARM.exidx.text.uxQueueSpacesAvailable), (8 bytes).
    Removing queue.o(.text.uxQueueMessagesWaitingFromISR), (12 bytes).
    Removing queue.o(.ARM.exidx.text.uxQueueMessagesWaitingFromISR), (8 bytes).
    Removing queue.o(.text.vQueueDelete), (140 bytes).
    Removing queue.o(.ARM.exidx.text.vQueueDelete), (8 bytes).
    Removing queue.o(.text.vQueueUnregisterQueue), (132 bytes).
    Removing queue.o(.ARM.exidx.text.vQueueUnregisterQueue), (8 bytes).
    Removing queue.o(.text.uxQueueGetQueueNumber), (4 bytes).
    Removing queue.o(.ARM.exidx.text.uxQueueGetQueueNumber), (8 bytes).
    Removing queue.o(.text.vQueueSetQueueNumber), (4 bytes).
    Removing queue.o(.ARM.exidx.text.vQueueSetQueueNumber), (8 bytes).
    Removing queue.o(.text.ucQueueGetQueueType), (6 bytes).
    Removing queue.o(.ARM.exidx.text.ucQueueGetQueueType), (8 bytes).
    Removing queue.o(.text.xQueueIsQueueEmptyFromISR), (16 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueIsQueueEmptyFromISR), (8 bytes).
    Removing queue.o(.text.xQueueIsQueueFullFromISR), (20 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueIsQueueFullFromISR), (8 bytes).
    Removing queue.o(.ARM.exidx.text.vQueueAddToRegistry), (8 bytes).
    Removing queue.o(.text.pcQueueGetName), (132 bytes).
    Removing queue.o(.ARM.exidx.text.pcQueueGetName), (8 bytes).
    Removing queue.o(.ARM.exidx.text.vQueueWaitForMessageRestricted), (8 bytes).
    Removing tasks.o(.text), (0 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskCreate), (8 bytes).
    Removing tasks.o(.text.vTaskDelete), (176 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskDelete), (8 bytes).
    Removing tasks.o(.text.xTaskDelayUntil), (164 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskDelayUntil), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskSuspendAll), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskResumeAll), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskDelay), (8 bytes).
    Removing tasks.o(.text.eTaskGetState), (136 bytes).
    Removing tasks.o(.ARM.exidx.text.eTaskGetState), (8 bytes).
    Removing tasks.o(.text.uxTaskPriorityGet), (40 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskPriorityGet), (8 bytes).
    Removing tasks.o(.text.uxTaskPriorityGetFromISR), (40 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskPriorityGetFromISR), (8 bytes).
    Removing tasks.o(.text.vTaskPrioritySet), (212 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskPrioritySet), (8 bytes).
    Removing tasks.o(.text.vTaskSuspend), (356 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskSuspend), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskSwitchContext), (8 bytes).
    Removing tasks.o(.text.vTaskResume), (124 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskResume), (8 bytes).
    Removing tasks.o(.text.xTaskResumeFromISR), (160 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskResumeFromISR), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskStartScheduler), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.prvIdleTask), (8 bytes).
    Removing tasks.o(.text.vTaskEndScheduler), (20 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskEndScheduler), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskIncrementTick), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGetTickCount), (8 bytes).
    Removing tasks.o(.text.xTaskGetTickCountFromISR), (12 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGetTickCountFromISR), (8 bytes).
    Removing tasks.o(.text.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskGetNumberOfTasks), (8 bytes).
    Removing tasks.o(.text.pcTaskGetName), (24 bytes).
    Removing tasks.o(.ARM.exidx.text.pcTaskGetName), (8 bytes).
    Removing tasks.o(.text.uxTaskGetSystemState), (1132 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskGetSystemState), (8 bytes).
    Removing tasks.o(.text.xTaskCatchUpTicks), (48 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskCatchUpTicks), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskPlaceOnEventList), (8 bytes).
    Removing tasks.o(.text.vTaskPlaceOnUnorderedEventList), (196 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskPlaceOnUnorderedEventList), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskPlaceOnEventListRestricted), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskRemoveFromEventList), (8 bytes).
    Removing tasks.o(.text.vTaskRemoveFromUnorderedEventList), (160 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskRemoveFromUnorderedEventList), (8 bytes).
    Removing tasks.o(.text.vTaskSetTimeOutState), (36 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskSetTimeOutState), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskInternalSetTimeOutState), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskCheckForTimeOut), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskMissedYield), (8 bytes).
    Removing tasks.o(.text.uxTaskGetTaskNumber), (12 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskGetTaskNumber), (8 bytes).
    Removing tasks.o(.text.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(.text.vTaskGetInfo), (304 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskGetInfo), (8 bytes).
    Removing tasks.o(.text.uxTaskGetStackHighWaterMark), (88 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskGetStackHighWaterMark), (8 bytes).
    Removing tasks.o(.text.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGetCurrentTaskHandle), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGetSchedulerState), (8 bytes).
    Removing tasks.o(.text.xTaskPriorityInherit), (156 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskPriorityInherit), (8 bytes).
    Removing tasks.o(.text.xTaskPriorityDisinherit), (124 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskPriorityDisinherit), (8 bytes).
    Removing tasks.o(.text.vTaskPriorityDisinheritAfterTimeout), (144 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskPriorityDisinheritAfterTimeout), (8 bytes).
    Removing tasks.o(.text.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskResetEventItemValue), (8 bytes).
    Removing tasks.o(.text.pvTaskIncrementMutexHeldCount), (24 bytes).
    Removing tasks.o(.ARM.exidx.text.pvTaskIncrementMutexHeldCount), (8 bytes).
    Removing tasks.o(.text.ulTaskGenericNotifyTake), (224 bytes).
    Removing tasks.o(.ARM.exidx.text.ulTaskGenericNotifyTake), (8 bytes).
    Removing tasks.o(.text.xTaskGenericNotifyWait), (260 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGenericNotifyWait), (8 bytes).
    Removing tasks.o(.text.xTaskGenericNotify), (256 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGenericNotify), (8 bytes).
    Removing tasks.o(.text.xTaskGenericNotifyFromISR), (308 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGenericNotifyFromISR), (8 bytes).
    Removing tasks.o(.text.vTaskGenericNotifyGiveFromISR), (220 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskGenericNotifyGiveFromISR), (8 bytes).
    Removing tasks.o(.text.xTaskGenericNotifyStateClear), (64 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGenericNotifyStateClear), (8 bytes).
    Removing tasks.o(.text.ulTaskGenericNotifyValueClear), (40 bytes).
    Removing tasks.o(.ARM.exidx.text.ulTaskGenericNotifyValueClear), (8 bytes).
    Removing timers.o(.text), (0 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerCreateTimerTask), (8 bytes).
    Removing timers.o(.ARM.exidx.text.prvTimerTask), (8 bytes).
    Removing timers.o(.text.xTimerCreate), (164 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerCreate), (8 bytes).
    Removing timers.o(.text.xTimerGenericCommand), (96 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerGenericCommand), (8 bytes).
    Removing timers.o(.text.xTimerGetTimerDaemonTaskHandle), (20 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerGetTimerDaemonTaskHandle), (8 bytes).
    Removing timers.o(.text.xTimerGetPeriod), (12 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerGetPeriod), (8 bytes).
    Removing timers.o(.text.vTimerSetReloadMode), (40 bytes).
    Removing timers.o(.ARM.exidx.text.vTimerSetReloadMode), (8 bytes).
    Removing timers.o(.text.xTimerGetReloadMode), (32 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerGetReloadMode), (8 bytes).
    Removing timers.o(.text.uxTimerGetReloadMode), (32 bytes).
    Removing timers.o(.ARM.exidx.text.uxTimerGetReloadMode), (8 bytes).
    Removing timers.o(.text.xTimerGetExpiryTime), (12 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerGetExpiryTime), (8 bytes).
    Removing timers.o(.text.pcTimerGetName), (12 bytes).
    Removing timers.o(.ARM.exidx.text.pcTimerGetName), (8 bytes).
    Removing timers.o(.text.xTimerIsTimerActive), (32 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerIsTimerActive), (8 bytes).
    Removing timers.o(.text.pvTimerGetTimerID), (26 bytes).
    Removing timers.o(.ARM.exidx.text.pvTimerGetTimerID), (8 bytes).
    Removing timers.o(.text.vTimerSetTimerID), (26 bytes).
    Removing timers.o(.ARM.exidx.text.vTimerSetTimerID), (8 bytes).
    Removing timers.o(.text.xTimerPendFunctionCallFromISR), (40 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerPendFunctionCallFromISR), (8 bytes).
    Removing timers.o(.text.xTimerPendFunctionCall), (48 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerPendFunctionCall), (8 bytes).
    Removing timers.o(.text.uxTimerGetTimerNumber), (4 bytes).
    Removing timers.o(.ARM.exidx.text.uxTimerGetTimerNumber), (8 bytes).
    Removing timers.o(.text.vTimerSetTimerNumber), (4 bytes).
    Removing timers.o(.ARM.exidx.text.vTimerSetTimerNumber), (8 bytes).
    Removing heap_4.o(.text), (0 bytes).
    Removing heap_4.o(.ARM.exidx.text.pvPortMalloc), (8 bytes).
    Removing heap_4.o(.ARM.exidx.text.vPortFree), (8 bytes).
    Removing heap_4.o(.text.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(.ARM.exidx.text.xPortGetFreeHeapSize), (8 bytes).
    Removing heap_4.o(.text.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing heap_4.o(.ARM.exidx.text.xPortGetMinimumEverFreeHeapSize), (8 bytes).
    Removing heap_4.o(.text.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(.ARM.exidx.text.vPortInitialiseBlocks), (8 bytes).
    Removing heap_4.o(.text.pvPortCalloc), (50 bytes).
    Removing heap_4.o(.ARM.exidx.text.pvPortCalloc), (8 bytes).
    Removing heap_4.o(.text.vPortGetHeapStats), (120 bytes).
    Removing heap_4.o(.ARM.exidx.text.vPortGetHeapStats), (8 bytes).
    Removing port.o(.text), (0 bytes).
    Removing port.o(.ARM.exidx.text.pxPortInitialiseStack), (8 bytes).
    Removing port.o(.ARM.exidx.text.prvTaskExitError), (8 bytes).
    Removing port.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing port.o(.ARM.exidx.text.xPortStartScheduler), (8 bytes).
    Removing port.o(.ARM.exidx.text.vPortSetupTimerInterrupt), (8 bytes).
    Removing port.o(.ARM.exidx.text.vPortStartFirstTask), (8 bytes).
    Removing port.o(.text.vPortEndScheduler), (24 bytes).
    Removing port.o(.ARM.exidx.text.vPortEndScheduler), (8 bytes).
    Removing port.o(.ARM.exidx.text.vPortYield), (8 bytes).
    Removing port.o(.ARM.exidx.text.vPortEnterCritical), (8 bytes).
    Removing port.o(.ARM.exidx.text.vPortExitCritical), (8 bytes).
    Removing port.o(.ARM.exidx.text.ulSetInterruptMaskFromISR), (8 bytes).
    Removing port.o(.ARM.exidx.text.vClearInterruptMaskFromISR), (8 bytes).
    Removing port.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing port.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing startuptask.o(.text), (0 bytes).
    Removing startuptask.o(.ARM.exidx.text.StartupTask), (8 bytes).
    Removing startuptask.o(.ARM.exidx.text.RGB_task), (8 bytes).
    Removing startuptask.o(.ARM.exidx.text.OLED_task), (8 bytes).
    Removing startuptask.o(.ARM.exidx.text.main_task), (8 bytes).
    Removing startuptask.o(.ARM.exidx.text.line_task), (8 bytes).
    Removing startuptask.o(.text.vApplicationStackOverflowHook), (2 bytes).
    Removing startuptask.o(.ARM.exidx.text.vApplicationStackOverflowHook), (8 bytes).
    Removing startuptask.o(.text.vApplicationMallocFailedHook), (2 bytes).
    Removing startuptask.o(.ARM.exidx.text.vApplicationMallocFailedHook), (8 bytes).
    Removing startuptask.o(.text.vApplicationIdleHook), (2 bytes).
    Removing startuptask.o(.ARM.exidx.text.vApplicationIdleHook), (8 bytes).
    Removing startuptask.o(.text.vApplicationTickHook), (2 bytes).
    Removing startuptask.o(.ARM.exidx.text.vApplicationTickHook), (8 bytes).
    Removing startuptask.o(.data.YunTai_EN), (4 bytes).
    Removing emm_v5.o(.text), (0 bytes).
    Removing emm_v5.o(.ARM.exidx.text.ZDT_Init), (8 bytes).
    Removing emm_v5.o(.text.select_ZDT), (12 bytes).
    Removing emm_v5.o(.ARM.exidx.text.select_ZDT), (8 bytes).
    Removing emm_v5.o(.text.ZDT_SetSpeed), (248 bytes).
    Removing emm_v5.o(.ARM.exidx.text.ZDT_SetSpeed), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Vel_Control), (132 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Vel_Control), (8 bytes).
    Removing emm_v5.o(.text.usart_SendByte), (36 bytes).
    Removing emm_v5.o(.ARM.exidx.text.usart_SendByte), (8 bytes).
    Removing emm_v5.o(.text.usart_SendCmd), (80 bytes).
    Removing emm_v5.o(.ARM.exidx.text.usart_SendCmd), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Reset_CurPos_To_Zero), (100 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_CurPos_To_Zero), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Reset_Clog_Pro), (100 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_Clog_Pro), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Read_Sys_Params), (268 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Read_Sys_Params), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Modify_Ctrl_Mode), (116 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Modify_Ctrl_Mode), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_En_Control), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Pos_Control), (144 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Pos_Control), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Stop_Now), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Synchronous_motion), (100 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Synchronous_motion), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Origin_Set_O), (112 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Set_O), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Origin_Modify_Params), (168 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Modify_Params), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Origin_Trigger_Return), (112 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Trigger_Return), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Origin_Interrupt), (100 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Interrupt), (8 bytes).
    Removing emm_v5.o(.bss.ZDT_Switch), (1 bytes).
    Removing ws2812b.o(.text), (0 bytes).
    Removing ws2812b.o(.text.WS2812_RESET), (532 bytes).
    Removing ws2812b.o(.ARM.exidx.text.WS2812_RESET), (8 bytes).
    Removing ws2812b.o(.text.WS2812_Set_Color), (20 bytes).
    Removing ws2812b.o(.ARM.exidx.text.WS2812_Set_Color), (8 bytes).
    Removing ws2812b.o(.text.WS2812_Send_Array), (576 bytes).
    Removing ws2812b.o(.ARM.exidx.text.WS2812_Send_Array), (8 bytes).
    Removing ws2812b.o(.bss.LedsArray), (3 bytes).
    Removing uart.o(.text), (0 bytes).
    Removing servo.o(.text), (0 bytes).
    Removing servo.o(.text.DL_Timer_setCaptureCompareValue2), (16 bytes).
    Removing servo.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue2), (8 bytes).
    Removing servo.o(.text.Set_Servo), (80 bytes).
    Removing servo.o(.ARM.exidx.text.Set_Servo), (8 bytes).
    Removing servo.o(.text.Set_Servo_raw), (24 bytes).
    Removing servo.o(.ARM.exidx.text.Set_Servo_raw), (8 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.delay), (2 bytes).
    Removing oled.o(.ARM.exidx.text.delay), (8 bytes).
    Removing oled.o(.text.IIC_Start), (64 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_Start), (8 bytes).
    Removing oled.o(.text.IIC_Stop), (44 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_Stop), (8 bytes).
    Removing oled.o(.text.IIC_Ack), (32 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_Ack), (8 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_XIE), (8 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_XIE_ML), (8 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_XIE_DATA), (8 bytes).
    Removing oled.o(.text.OLED_Set_Pos), (30 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Set_Pos), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.text.OLED_ShowString), (74 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled.o(.text.OLED_ShowChinese), (272 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChinese), (8 bytes).
    Removing oled.o(.text.OLED_DrawBMP), (328 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawBMP), (8 bytes).
    Removing oled.o(.text.OLED_IIC_Init), (2 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_IIC_Init), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Write), (8 bytes).
    Removing oled.o(.data.Hzk), (64 bytes).
    Removing oled.o(.data.Hzk2), (64 bytes).
    Removing motor.o(.text), (0 bytes).
    Removing motor.o(.text.Set_Motor), (140 bytes).
    Removing motor.o(.ARM.exidx.text.Set_Motor), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Write), (8 bytes).
    Removing led_key.o(.text), (0 bytes).
    Removing led_key.o(.ARM.exidx.text.GPIO_WriteBit), (8 bytes).
    Removing enconder.o(.text), (0 bytes).
    Removing enconder.o(.ARM.exidx.text.Get_Encoder), (8 bytes).
    Removing enconder.o(.ARM.exidx.text.Encoder_init), (8 bytes).
    Removing enconder.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing delay.o(.text), (0 bytes).
    Removing delay.o(.ARM.exidx.text.delay_noOS), (8 bytes).
    Removing delay.o(.bss.delay_times), (4 bytes).
    Removing control.o(.text), (0 bytes).
    Removing control.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing control.o(.text.Erect_pid), (86 bytes).
    Removing control.o(.ARM.exidx.text.Erect_pid), (8 bytes).
    Removing control.o(.text.Get_hw), (96 bytes).
    Removing control.o(.ARM.exidx.text.Get_hw), (8 bytes).
    Removing control.o(.ARM.exidx.text.Line_Control), (8 bytes).
    Removing control.o(.text.Line_Control_Simple), (236 bytes).
    Removing control.o(.ARM.exidx.text.Line_Control_Simple), (8 bytes).
    Removing control.o(.text.Motor_Open), (236 bytes).
    Removing control.o(.ARM.exidx.text.Motor_Open), (8 bytes).
    Removing maixcam.o(.text), (0 bytes).
    Removing maixcam.o(.text.UART2_Init), (24 bytes).
    Removing maixcam.o(.ARM.exidx.text.UART2_Init), (8 bytes).
    Removing maixcam.o(.text.uart2_send_char), (36 bytes).
    Removing maixcam.o(.ARM.exidx.text.uart2_send_char), (8 bytes).
    Removing maixcam.o(.text.uart2_send_string), (56 bytes).
    Removing maixcam.o(.ARM.exidx.text.uart2_send_string), (8 bytes).
    Removing maixcam.o(.text.fputc), (36 bytes).
    Removing maixcam.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing maixcam.o(.ARM.exidx.text.MaixCam_Init), (8 bytes).
    Removing maixcam.o(.text.MaixCam_Send_XY), (176 bytes).
    Removing maixcam.o(.ARM.exidx.text.MaixCam_Send_XY), (8 bytes).
    Removing maixcam.o(.ARM.exidx.text.UART1_IRQHandler), (8 bytes).
    Removing fadd.o(.text), (178 bytes).
    Removing fmul.o(.text), (122 bytes).
    Removing fdiv.o(.text), (124 bytes).
    Removing fcmpgt.o(.text), (28 bytes).
    Removing ffixi.o(.text), (50 bytes).
    Removing fepilogue.o(.text), (130 bytes).

1029 unused section(s) (total 40766 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llmul.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpgt.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../lib/ti/devices/msp/m0p/startup_system_files/keil/startup_mspm0g350x_uvision.s 0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    Emm_V5.c                                 0x00000000   Number         0  emm_v5.o ABSOLUTE
    Enconder.c                               0x00000000   Number         0  enconder.o ABSOLUTE
    LED_Key.c                                0x00000000   Number         0  led_key.o ABSOLUTE
    MaixCam.c                                0x00000000   Number         0  maixcam.o ABSOLUTE
    Motor.c                                  0x00000000   Number         0  motor.o ABSOLUTE
    StartupTask.c                            0x00000000   Number         0  startuptask.o ABSOLUTE
    UART.c                                   0x00000000   Number         0  uart.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    control.c                                0x00000000   Number         0  control.o ABSOLUTE
    croutine.c                               0x00000000   Number         0  croutine.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    delay.c                                  0x00000000   Number         0  delay.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_aes.c                                 0x00000000   Number         0  dl_aes.o ABSOLUTE
    dl_aesadv.c                              0x00000000   Number         0  dl_aesadv.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_crc.c                                 0x00000000   Number         0  dl_crc.o ABSOLUTE
    dl_crcp.c                                0x00000000   Number         0  dl_crcp.o ABSOLUTE
    dl_dac12.c                               0x00000000   Number         0  dl_dac12.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_flashctl.c                            0x00000000   Number         0  dl_flashctl.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_interrupt.c                           0x00000000   Number         0  dl_interrupt.o ABSOLUTE
    dl_keystorectl.c                         0x00000000   Number         0  dl_keystorectl.o ABSOLUTE
    dl_lcd.c                                 0x00000000   Number         0  dl_lcd.o ABSOLUTE
    dl_lfss.c                                0x00000000   Number         0  dl_lfss.o ABSOLUTE
    dl_mathacl.c                             0x00000000   Number         0  dl_mathacl.o ABSOLUTE
    dl_mcan.c                                0x00000000   Number         0  dl_mcan.o ABSOLUTE
    dl_opa.c                                 0x00000000   Number         0  dl_opa.o ABSOLUTE
    dl_rtc_common.c                          0x00000000   Number         0  dl_rtc_common.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_trng.c                                0x00000000   Number         0  dl_trng.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    dl_vref.c                                0x00000000   Number         0  dl_vref.o ABSOLUTE
    event_groups.c                           0x00000000   Number         0  event_groups.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    heap_4.c                                 0x00000000   Number         0  heap_4.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    list.c                                   0x00000000   Number         0  list.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    oled.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    port.c                                   0x00000000   Number         0  port.o ABSOLUTE
    queue.c                                  0x00000000   Number         0  queue.o ABSOLUTE
    servo.c                                  0x00000000   Number         0  servo.o ABSOLUTE
    tasks.c                                  0x00000000   Number         0  tasks.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    timers.c                                 0x00000000   Number         0  timers.o ABSOLUTE
    ws2812b.c                                0x00000000   Number         0  ws2812b.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       20  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000000e8   Section        0  llmul.o(.text)
    .text                                    0x00000118   Section        0  memcpya.o(.text)
    .text                                    0x0000013c   Section        0  memseta.o(.text)
    .text                                    0x00000160   Section        0  uidiv_div0.o(.text)
    .text                                    0x000001a0   Section        0  idiv_div0.o(.text)
    .text                                    0x000001f0   Section        0  uldiv.o(.text)
    .text                                    0x00000250   Section        0  iusefp.o(.text)
    .text                                    0x00000250   Section        0  dadd.o(.text)
    .text                                    0x000003b4   Section        0  dmul.o(.text)
    .text                                    0x00000484   Section        0  ddiv.o(.text)
    .text                                    0x00000574   Section        0  dfixul.o(.text)
    .text                                    0x000005b4   Section       40  cdrcmple.o(.text)
    .text                                    0x000005e0   Section       36  init.o(.text)
    .text                                    0x00000604   Section        0  llshl.o(.text)
    .text                                    0x00000624   Section        0  llushr.o(.text)
    .text                                    0x00000646   Section        0  llsshr.o(.text)
    .text                                    0x0000066c   Section        0  depilogue.o(.text)
    [Anonymous Symbol]                       0x0000072a   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x00000734   Section        0  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_0                             0x00000770   Number         4  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_1                             0x00000774   Number         4  dl_spi.o(.text.DL_SPI_init)
    [Anonymous Symbol]                       0x00000778   Section        0  dl_spi.o(.text.DL_SPI_setClockConfig)
    [Anonymous Symbol]                       0x0000078c   Section        0  dl_timer.o(.text.DL_TimerA_initPWMMode)
    [Anonymous Symbol]                       0x00000880   Section        0  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_1                            0x0000092c   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_2                            0x00000930   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_3                            0x00000934   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_4                            0x00000938   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_6                            0x0000093c   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    [Anonymous Symbol]                       0x00000940   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x00000a1c   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_1                             0x00000a20   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x00000a24   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    [Anonymous Symbol]                       0x00000a28   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.26_0                            0x00000a40   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00000a44   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.13_0                            0x00000a58   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00000a5c   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00000a68   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00000a6c   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00000a84   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    [Anonymous Symbol]                       0x00000a88   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00000ac8   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00000acc   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x00000ad0   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x00000ae4   Section        0  emm_v5.o(.text.Emm_V5_En_Control)
    [Anonymous Symbol]                       0x00000b54   Section        0  emm_v5.o(.text.Emm_V5_Stop_Now)
    __arm_cp.12_0                            0x00000bc0   Number         4  emm_v5.o(.text.Emm_V5_Stop_Now)
    [Anonymous Symbol]                       0x00000bc4   Section        0  enconder.o(.text.Encoder_init)
    __arm_cp.1_0                             0x00000bd0   Number         4  enconder.o(.text.Encoder_init)
    __arm_cp.1_1                             0x00000bd4   Number         4  enconder.o(.text.Encoder_init)
    [Anonymous Symbol]                       0x00000bd8   Section        0  led_key.o(.text.GPIO_WriteBit)
    __arm_cp.0_0                             0x00000bf0   Number         4  led_key.o(.text.GPIO_WriteBit)
    [Anonymous Symbol]                       0x00000bf4   Section        0  enconder.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_0                             0x00000c24   Number         4  enconder.o(.text.GROUP1_IRQHandler)
    __arm_cp.2_1                             0x00000c28   Number         4  enconder.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x00000c2c   Section        0  enconder.o(.text.Get_Encoder)
    __arm_cp.0_0                             0x00000c40   Number         4  enconder.o(.text.Get_Encoder)
    [Anonymous Symbol]                       0x00000c44   Section        0  oled.o(.text.IIC_XIE)
    [Anonymous Symbol]                       0x00000d84   Section        0  oled.o(.text.IIC_XIE_DATA)
    [Anonymous Symbol]                       0x00000e34   Section        0  oled.o(.text.IIC_XIE_ML)
    __arm_cp.5_0                             0x00000ee4   Number         4  oled.o(.text.IIC_XIE_ML)
    [Anonymous Symbol]                       0x00000ee8   Section        0  control.o(.text.Line_Control)
    __arm_cp.3_0                             0x000010dc   Number         4  control.o(.text.Line_Control)
    __arm_cp.3_1                             0x000010e0   Number         4  control.o(.text.Line_Control)
    __arm_cp.3_2                             0x000010e4   Number         4  control.o(.text.Line_Control)
    __arm_cp.3_3                             0x000010e8   Number         4  control.o(.text.Line_Control)
    __arm_cp.3_4                             0x000010ec   Number         4  control.o(.text.Line_Control)
    __arm_cp.3_5                             0x000010f0   Number         4  control.o(.text.Line_Control)
    [Anonymous Symbol]                       0x000010f4   Section        0  maixcam.o(.text.MaixCam_Init)
    __arm_cp.4_0                             0x00001104   Number         4  maixcam.o(.text.MaixCam_Init)
    __arm_cp.4_1                             0x00001108   Number         4  maixcam.o(.text.MaixCam_Init)
    [Anonymous Symbol]                       0x0000110c   Section        0  motor.o(.text.Motor_Write)
    __arm_cp.1_0                             0x00001198   Number         4  motor.o(.text.Motor_Write)
    __arm_cp.1_1                             0x0000119c   Number         4  motor.o(.text.Motor_Write)
    __arm_cp.1_2                             0x000011a0   Number         4  motor.o(.text.Motor_Write)
    __arm_cp.1_3                             0x000011a4   Number         4  motor.o(.text.Motor_Write)
    __arm_cp.1_4                             0x000011a8   Number         4  motor.o(.text.Motor_Write)
    [Anonymous Symbol]                       0x000011ac   Section        0  oled.o(.text.OLED_Clear)
    __arm_cp.8_0                             0x0000128c   Number         4  oled.o(.text.OLED_Clear)
    [Anonymous Symbol]                       0x00001290   Section        0  oled.o(.text.OLED_Init)
    __arm_cp.14_0                            0x00001394   Number         4  oled.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x00001398   Section        0  oled.o(.text.OLED_ShowChar)
    __arm_cp.9_0                             0x0000148c   Number         4  oled.o(.text.OLED_ShowChar)
    __arm_cp.9_1                             0x00001490   Number         4  oled.o(.text.OLED_ShowChar)
    [Anonymous Symbol]                       0x00001494   Section        0  oled.o(.text.OLED_Write)
    __arm_cp.15_0                            0x00001520   Number         4  oled.o(.text.OLED_Write)
    [Anonymous Symbol]                       0x00001524   Section        0  startuptask.o(.text.OLED_task)
    __arm_cp.2_0                             0x0000159c   Number         4  startuptask.o(.text.OLED_task)
    __arm_cp.2_1                             0x000015a0   Number         4  startuptask.o(.text.OLED_task)
    [Anonymous Symbol]                       0x000015ac   Section        0  control.o(.text.PID_Init)
    __arm_cp.0_0                             0x000015c4   Number         4  control.o(.text.PID_Init)
    __arm_cp.0_1                             0x000015c8   Number         4  control.o(.text.PID_Init)
    __arm_cp.0_2                             0x000015cc   Number         4  control.o(.text.PID_Init)
    __arm_cp.0_3                             0x000015d0   Number         4  control.o(.text.PID_Init)
    [Anonymous Symbol]                       0x000015e0   Section        0  port.o(.text.PendSV_Handler)
    pxCurrentTCBConst                        0x00001620   Number         0  port.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x00001624   Section        0  startuptask.o(.text.RGB_task)
    [Anonymous Symbol]                       0x00001634   Section        0  port.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x00001638   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x000016f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x000016f4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x000016f8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x000016fc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00001700   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00001704   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00001708   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x0000170c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_8                             0x00001710   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_9                             0x00001714   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00001718   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_0                             0x00001790   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_1                             0x00001794   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_2                             0x00001798   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_3                             0x0000179c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_5                             0x000017a0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_6                             0x000017a4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    [Anonymous Symbol]                       0x000017a8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    __arm_cp.9_0                             0x000017e0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    __arm_cp.9_1                             0x000017e4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    __arm_cp.9_2                             0x000017e8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    __arm_cp.9_3                             0x000017ec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    __arm_cp.9_4                             0x000017f0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    [Anonymous Symbol]                       0x000017f4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00001838   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x0000183c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_2                             0x00001840   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00001844   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00001848   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_0                             0x00001904   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_1                             0x00001908   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_2                             0x0000190c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_3                             0x00001910   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_4                             0x00001914   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_5                             0x00001918   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    [Anonymous Symbol]                       0x0000191c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_0                             0x0000194c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_1                             0x00001950   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_2                             0x00001954   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_3                             0x00001958   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_4                             0x0000195c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.6_5                             0x00001960   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00001964   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.8_0                             0x000019bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.8_1                             0x000019c0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.8_2                             0x000019c4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.8_3                             0x000019c8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.8_5                             0x000019cc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    [Anonymous Symbol]                       0x000019d0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init)
    __arm_cp.7_0                             0x00001a38   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init)
    __arm_cp.7_1                             0x00001a3c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init)
    __arm_cp.7_2                             0x00001a40   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init)
    __arm_cp.7_3                             0x00001a44   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init)
    __arm_cp.7_4                             0x00001a48   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init)
    __arm_cp.7_5                             0x00001a4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init)
    __arm_cp.7_6                             0x00001a50   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init)
    [Anonymous Symbol]                       0x00001a54   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00001a94   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x00001a98   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_2                             0x00001a9c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00001aa0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00001ae8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00001aec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00001af0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00001af4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00001af8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00001afc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00001b00   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_7                             0x00001b04   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_8                             0x00001b08   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_9                             0x00001b0c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00001b10   Section        0  startuptask.o(.text.StartupTask)
    __arm_cp.0_0                             0x00001b6c   Number         4  startuptask.o(.text.StartupTask)
    __arm_cp.0_1                             0x00001b70   Number         4  startuptask.o(.text.StartupTask)
    __arm_cp.0_3                             0x00001b78   Number         4  startuptask.o(.text.StartupTask)
    __arm_cp.0_5                             0x00001b84   Number         4  startuptask.o(.text.StartupTask)
    __arm_cp.0_7                             0x00001b90   Number         4  startuptask.o(.text.StartupTask)
    [Anonymous Symbol]                       0x00001b9c   Section        0  port.o(.text.SysTick_Handler)
    __arm_cp.13_0                            0x00001bbc   Number         4  port.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x00001bc0   Section        0  maixcam.o(.text.UART1_IRQHandler)
    __arm_cp.6_0                             0x00001c3c   Number         4  maixcam.o(.text.UART1_IRQHandler)
    __arm_cp.6_1                             0x00001c40   Number         4  maixcam.o(.text.UART1_IRQHandler)
    __arm_cp.6_3                             0x00001c44   Number         4  maixcam.o(.text.UART1_IRQHandler)
    [Anonymous Symbol]                       0x00001c48   Section        0  emm_v5.o(.text.ZDT_Init)
    __arm_cp.0_0                             0x00001c54   Number         4  emm_v5.o(.text.ZDT_Init)
    [Anonymous Symbol]                       0x00001c58   Section        0  delay.o(.text.delay_noOS)
    __arm_cp.0_0                             0x00001cd0   Number         4  delay.o(.text.delay_noOS)
    [Anonymous Symbol]                       0x00001cd4   Section        0  startuptask.o(.text.line_task)
    __arm_cp.4_0                             0x00001cf0   Number         4  startuptask.o(.text.line_task)
    [Anonymous Symbol]                       0x00001cf4   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x00001d30   Section        0  startuptask.o(.text.main_task)
    __arm_cp.3_1                             0x00001db4   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_2                             0x00001db8   Number         4  startuptask.o(.text.main_task)
    prvIdleTask                              0x00001dbd   Thumb Code    68  tasks.o(.text.prvIdleTask)
    [Anonymous Symbol]                       0x00001dbc   Section        0  tasks.o(.text.prvIdleTask)
    __arm_cp.15_0                            0x00001e00   Number         4  tasks.o(.text.prvIdleTask)
    __arm_cp.15_1                            0x00001e04   Number         4  tasks.o(.text.prvIdleTask)
    prvTaskExitError                         0x00001e09   Thumb Code    48  port.o(.text.prvTaskExitError)
    [Anonymous Symbol]                       0x00001e08   Section        0  port.o(.text.prvTaskExitError)
    __arm_cp.1_0                             0x00001e38   Number         4  port.o(.text.prvTaskExitError)
    prvTimerTask                             0x00001e3d   Thumb Code   764  timers.o(.text.prvTimerTask)
    [Anonymous Symbol]                       0x00001e3c   Section        0  timers.o(.text.prvTimerTask)
    __arm_cp.1_0                             0x00002138   Number         4  timers.o(.text.prvTimerTask)
    prvUnlockQueue                           0x0000213d   Thumb Code   126  queue.o(.text.prvUnlockQueue)
    [Anonymous Symbol]                       0x0000213c   Section        0  queue.o(.text.prvUnlockQueue)
    [Anonymous Symbol]                       0x000021bc   Section        0  heap_4.o(.text.pvPortMalloc)
    __arm_cp.0_1                             0x00002330   Number         4  heap_4.o(.text.pvPortMalloc)
    [Anonymous Symbol]                       0x00002334   Section        0  port.o(.text.pxPortInitialiseStack)
    __arm_cp.0_0                             0x00002354   Number         4  port.o(.text.pxPortInitialiseStack)
    [Anonymous Symbol]                       0x00002358   Section        0  port.o(.text.ulSetInterruptMaskFromISR)
    [Anonymous Symbol]                       0x00002360   Section        0  list.o(.text.uxListRemove)
    [Anonymous Symbol]                       0x00002382   Section        0  port.o(.text.vClearInterruptMaskFromISR)
    [Anonymous Symbol]                       0x00002388   Section        0  list.o(.text.vListInitialise)
    [Anonymous Symbol]                       0x0000239c   Section        0  list.o(.text.vListInitialiseItem)
    [Anonymous Symbol]                       0x000023a2   Section        0  list.o(.text.vListInsert)
    [Anonymous Symbol]                       0x000023e0   Section        0  port.o(.text.vPortEnterCritical)
    [Anonymous Symbol]                       0x000023f4   Section        0  port.o(.text.vPortExitCritical)
    __arm_cp.9_0                             0x0000240c   Number         4  port.o(.text.vPortExitCritical)
    [Anonymous Symbol]                       0x00002410   Section        0  heap_4.o(.text.vPortFree)
    __arm_cp.1_0                             0x000024a4   Number         4  heap_4.o(.text.vPortFree)
    [Anonymous Symbol]                       0x000024a8   Section        0  port.o(.text.vPortSetupTimerInterrupt)
    __arm_cp.4_0                             0x000024bc   Number         4  port.o(.text.vPortSetupTimerInterrupt)
    __arm_cp.4_1                             0x000024c0   Number         4  port.o(.text.vPortSetupTimerInterrupt)
    vPortStartFirstTask                      0x000024d1   Thumb Code    52  port.o(.text.vPortStartFirstTask)
    [Anonymous Symbol]                       0x000024d0   Section        0  port.o(.text.vPortStartFirstTask)
    pxCurrentTCBConst2                       0x00002500   Number         0  port.o(.text.vPortStartFirstTask)
    [Anonymous Symbol]                       0x00002504   Section        0  port.o(.text.vPortYield)
    __arm_cp.7_0                             0x00002518   Number         4  port.o(.text.vPortYield)
    [Anonymous Symbol]                       0x0000251c   Section        0  queue.o(.text.vQueueAddToRegistry)
    __arm_cp.27_0                            0x00002610   Number         4  queue.o(.text.vQueueAddToRegistry)
    [Anonymous Symbol]                       0x00002614   Section        0  queue.o(.text.vQueueWaitForMessageRestricted)
    [Anonymous Symbol]                       0x00002670   Section        0  tasks.o(.text.vTaskDelay)
    [Anonymous Symbol]                       0x000026e4   Section        0  tasks.o(.text.vTaskInternalSetTimeOutState)
    [Anonymous Symbol]                       0x000026f0   Section        0  tasks.o(.text.vTaskMissedYield)
    [Anonymous Symbol]                       0x000026f8   Section        0  tasks.o(.text.vTaskPlaceOnEventList)
    [Anonymous Symbol]                       0x00002778   Section        0  tasks.o(.text.vTaskPlaceOnEventListRestricted)
    [Anonymous Symbol]                       0x00002818   Section        0  tasks.o(.text.vTaskStartScheduler)
    __arm_cp.14_0                            0x00002860   Number         4  tasks.o(.text.vTaskStartScheduler)
    __arm_cp.14_1                            0x00002864   Number         4  tasks.o(.text.vTaskStartScheduler)
    __arm_cp.14_3                            0x00002870   Number         4  tasks.o(.text.vTaskStartScheduler)
    [Anonymous Symbol]                       0x00002874   Section        0  tasks.o(.text.vTaskSuspendAll)
    __tagsym$$used.0                         0x00002880   Number         0  tasks.o(.text.vTaskSwitchContext)
    [Anonymous Symbol]                       0x00002880   Section        0  tasks.o(.text.vTaskSwitchContext)
    __arm_cp.11_0                            0x00002908   Number         4  tasks.o(.text.vTaskSwitchContext)
    __arm_cp.11_1                            0x0000290c   Number         4  tasks.o(.text.vTaskSwitchContext)
    __arm_cp.11_2                            0x00002910   Number         4  tasks.o(.text.vTaskSwitchContext)
    [Anonymous Symbol]                       0x00002914   Section        0  port.o(.text.xPortStartScheduler)
    __arm_cp.3_0                             0x00002968   Number         4  port.o(.text.xPortStartScheduler)
    __arm_cp.3_1                             0x0000296c   Number         4  port.o(.text.xPortStartScheduler)
    [Anonymous Symbol]                       0x00002970   Section        0  queue.o(.text.xQueueGenericCreate)
    [Anonymous Symbol]                       0x00002a06   Section        0  queue.o(.text.xQueueReceive)
    [Anonymous Symbol]                       0x00002b48   Section        0  tasks.o(.text.xTaskCheckForTimeOut)
    __arm_cp.31_0                            0x00002bbc   Number         4  tasks.o(.text.xTaskCheckForTimeOut)
    [Anonymous Symbol]                       0x00002bc0   Section        0  tasks.o(.text.xTaskCreate)
    __arm_cp.0_1                             0x00002fec   Number         4  tasks.o(.text.xTaskCreate)
    __arm_cp.0_2                             0x00002ff0   Number         4  tasks.o(.text.xTaskCreate)
    __arm_cp.0_3                             0x00002ff4   Number         4  tasks.o(.text.xTaskCreate)
    __arm_cp.0_4                             0x00002ff8   Number         4  tasks.o(.text.xTaskCreate)
    __arm_cp.0_5                             0x00002ffc   Number         4  tasks.o(.text.xTaskCreate)
    [Anonymous Symbol]                       0x00003000   Section        0  tasks.o(.text.xTaskGetSchedulerState)
    [Anonymous Symbol]                       0x00003018   Section        0  tasks.o(.text.xTaskGetTickCount)
    __arm_cp.18_0                            0x00003020   Number         4  tasks.o(.text.xTaskGetTickCount)
    [Anonymous Symbol]                       0x00003024   Section        0  tasks.o(.text.xTaskIncrementTick)
    [Anonymous Symbol]                       0x00003154   Section        0  tasks.o(.text.xTaskRemoveFromEventList)
    [Anonymous Symbol]                       0x00003200   Section        0  tasks.o(.text.xTaskResumeAll)
    __arm_cp.4_0                             0x00003308   Number         4  tasks.o(.text.xTaskResumeAll)
    __arm_cp.4_1                             0x0000330c   Number         4  tasks.o(.text.xTaskResumeAll)
    __arm_cp.4_2                             0x00003310   Number         4  tasks.o(.text.xTaskResumeAll)
    [Anonymous Symbol]                       0x00003314   Section        0  timers.o(.text.xTimerCreateTimerTask)
    __arm_cp.0_0                             0x00003380   Number         4  timers.o(.text.xTimerCreateTimerTask)
    __arm_cp.0_1                             0x00003384   Number         4  timers.o(.text.xTimerCreateTimerTask)
    __arm_cp.0_2                             0x00003388   Number         4  timers.o(.text.xTimerCreateTimerTask)
    i.__0vsprintf                            0x00003394   Section        0  printfa.o(i.__0vsprintf)
    i.__ARM_clz                              0x000033b8   Section        0  depilogue.o(i.__ARM_clz)
    i.__scatterload_copy                     0x000033e8   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x000033f8   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00003400   Section       14  handlers.o(i.__scatterload_zeroinit)
    _fp_digits                               0x00003411   Thumb Code   344  printfa.o(i._fp_digits)
    i._fp_digits                             0x00003410   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x00003585   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_core                           0x00003584   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x00003c71   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x00003c70   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x00003c91   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x00003c90   Section        0  printfa.o(i._printf_pre_padding)
    _sputc                                   0x00003cbd   Thumb Code    10  printfa.o(i._sputc)
    i._sputc                                 0x00003cbc   Section        0  printfa.o(i._sputc)
    gMOTOR_PWMClockConfig                    0x000044de   Data           3  ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig)
    [Anonymous Symbol]                       0x000044de   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig)
    gMOTOR_PWMConfig                         0x000044e4   Data           8  ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig)
    [Anonymous Symbol]                       0x000044e4   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig)
    gSPI_WS2812_clockConfig                  0x000044ec   Data           2  ti_msp_dl_config.o(.rodata.gSPI_WS2812_clockConfig)
    [Anonymous Symbol]                       0x000044ec   Section        0  ti_msp_dl_config.o(.rodata.gSPI_WS2812_clockConfig)
    gSPI_WS2812_config                       0x000044ee   Data          10  ti_msp_dl_config.o(.rodata.gSPI_WS2812_config)
    [Anonymous Symbol]                       0x000044ee   Section        0  ti_msp_dl_config.o(.rodata.gSPI_WS2812_config)
    gServoClockConfig                        0x000044f8   Data           3  ti_msp_dl_config.o(.rodata.gServoClockConfig)
    [Anonymous Symbol]                       0x000044f8   Section        0  ti_msp_dl_config.o(.rodata.gServoClockConfig)
    gServoConfig                             0x000044fc   Data           8  ti_msp_dl_config.o(.rodata.gServoConfig)
    [Anonymous Symbol]                       0x000044fc   Section        0  ti_msp_dl_config.o(.rodata.gServoConfig)
    gTIMER_0ClockConfig                      0x00004504   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x00004504   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x00004508   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x00004508   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gUART_1ClockConfig                       0x0000451c   Data           2  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    [Anonymous Symbol]                       0x0000451c   Section        0  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    gUART_1Config                            0x0000451e   Data          10  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x0000451e   Section        0  ti_msp_dl_config.o(.rodata.gUART_1Config)
    gZDT1ClockConfig                         0x00004528   Data           2  ti_msp_dl_config.o(.rodata.gZDT1ClockConfig)
    [Anonymous Symbol]                       0x00004528   Section        0  ti_msp_dl_config.o(.rodata.gZDT1ClockConfig)
    gZDT1Config                              0x0000452a   Data          10  ti_msp_dl_config.o(.rodata.gZDT1Config)
    [Anonymous Symbol]                       0x0000452a   Section        0  ti_msp_dl_config.o(.rodata.gZDT1Config)
    [Anonymous Symbol]                       0x00004534   Section        0  timers.o(.rodata.str1.1)
    uxCriticalNesting                        0x20200008   Data           4  port.o(.data.uxCriticalNesting)
    [Anonymous Symbol]                       0x20200008   Section        0  port.o(.data.uxCriticalNesting)
    uxTaskNumber                             0x20200010   Data           4  tasks.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20200010   Section        0  tasks.o(.bss..L_MergedGlobals)
    uxDeletedTasksWaitingCleanUp             0x20200014   Data           4  tasks.o(.bss..L_MergedGlobals)
    uxCurrentNumberOfTasks                   0x20200018   Data           4  tasks.o(.bss..L_MergedGlobals)
    xSchedulerRunning                        0x2020001c   Data           4  tasks.o(.bss..L_MergedGlobals)
    uxSchedulerSuspended                     0x20200020   Data           4  tasks.o(.bss..L_MergedGlobals)
    xTickCount                               0x20200024   Data           4  tasks.o(.bss..L_MergedGlobals)
    pxDelayedTaskList                        0x20200028   Data           4  tasks.o(.bss..L_MergedGlobals)
    pxOverflowDelayedTaskList                0x2020002c   Data           4  tasks.o(.bss..L_MergedGlobals)
    uxTopReadyPriority                       0x20200030   Data           4  tasks.o(.bss..L_MergedGlobals)
    xYieldPending                            0x20200034   Data           4  tasks.o(.bss..L_MergedGlobals)
    xIdleTaskHandle                          0x20200038   Data           4  tasks.o(.bss..L_MergedGlobals)
    xNextTaskUnblockTime                     0x2020003c   Data           4  tasks.o(.bss..L_MergedGlobals)
    xPendedTicks                             0x20200040   Data           4  tasks.o(.bss..L_MergedGlobals)
    xNumOfOverflows                          0x20200044   Data           4  tasks.o(.bss..L_MergedGlobals)
    xTasksWaitingTermination                 0x20200048   Data          20  tasks.o(.bss..L_MergedGlobals)
    xSuspendedTaskList                       0x2020005c   Data          20  tasks.o(.bss..L_MergedGlobals)
    xPendingReadyList                        0x20200070   Data          20  tasks.o(.bss..L_MergedGlobals)
    xTimerQueue                              0x20200084   Data           4  timers.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20200084   Section        0  timers.o(.bss..L_MergedGlobals)
    xTimerTaskHandle                         0x20200088   Data           4  timers.o(.bss..L_MergedGlobals)
    pxCurrentTimerList                       0x2020008c   Data           4  timers.o(.bss..L_MergedGlobals)
    pxOverflowTimerList                      0x20200090   Data           4  timers.o(.bss..L_MergedGlobals)
    prvSampleTimeNow.xLastTime               0x20200094   Data           4  timers.o(.bss..L_MergedGlobals)
    xActiveTimerList1                        0x20200098   Data          20  timers.o(.bss..L_MergedGlobals)
    xActiveTimerList2                        0x202000ac   Data          20  timers.o(.bss..L_MergedGlobals)
    pxEnd                                    0x202000c0   Data           4  heap_4.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x202000c0   Section        0  heap_4.o(.bss..L_MergedGlobals)
    xFreeBytesRemaining                      0x202000c4   Data           4  heap_4.o(.bss..L_MergedGlobals)
    xMinimumEverFreeBytesRemaining           0x202000c8   Data           4  heap_4.o(.bss..L_MergedGlobals)
    xNumberOfSuccessfulAllocations           0x202000cc   Data           4  heap_4.o(.bss..L_MergedGlobals)
    xNumberOfSuccessfulFrees                 0x202000d0   Data           4  heap_4.o(.bss..L_MergedGlobals)
    xStart                                   0x202000d4   Data           8  heap_4.o(.bss..L_MergedGlobals)
    RGBTask_Handler                          0x202000dc   Data           4  startuptask.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x202000dc   Section        0  startuptask.o(.bss..L_MergedGlobals)
    OLEDTask_Handler                         0x202000e0   Data           4  startuptask.o(.bss..L_MergedGlobals)
    mainTask_Handler                         0x202000e4   Data           4  startuptask.o(.bss..L_MergedGlobals)
    lineTask_Handler                         0x202000e8   Data           4  startuptask.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x202000ec   Section        0  enconder.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x202000f0   Section        0  control.o(.bss..L_MergedGlobals)
    UART1_IRQHandler.i                       0x20200148   Data           1  maixcam.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20200148   Section        0  maixcam.o(.bss..L_MergedGlobals)
    UART1_IRQHandler.dataFlag                0x20200149   Data           1  maixcam.o(.bss..L_MergedGlobals)
    UART1_IRQHandler.MaixCam_data_buff       0x20200158   Data          40  maixcam.o(.bss..L_MergedGlobals)
    xDelayedTaskList1                        0x20200180   Data          20  tasks.o(.bss..L_MergedGlobals.1)
    [Anonymous Symbol]                       0x20200180   Section        0  tasks.o(.bss..L_MergedGlobals.1)
    xDelayedTaskList2                        0x20200194   Data          20  tasks.o(.bss..L_MergedGlobals.1)
    __tagsym$$used.1                         0x20200314   Number         0  tasks.o(.bss.pxCurrentTCB)
    pxReadyTasksLists                        0x20200318   Data        1120  tasks.o(.bss.pxReadyTasksLists)
    [Anonymous Symbol]                       0x20200318   Section        0  tasks.o(.bss.pxReadyTasksLists)
    ucHeap                                   0x20200778   Data       20480  heap_4.o(.bss.ucHeap)
    [Anonymous Symbol]                       0x20200778   Section        0  heap_4.o(.bss.ucHeap)
    STACK                                    0x202057b8   Section     2048  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000000d9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000000db   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000000e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __aeabi_lmul                             0x000000e9   Thumb Code    48  llmul.o(.text)
    _ll_mul                                  0x000000e9   Thumb Code     0  llmul.o(.text)
    __aeabi_memcpy                           0x00000119   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x00000119   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x00000119   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0000013d   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0000013d   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0000013d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0000014b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0000014b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0000014b   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0000014f   Thumb Code    18  memseta.o(.text)
    __aeabi_uidiv                            0x00000161   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x00000161   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_idiv                             0x000001a1   Thumb Code     0  idiv_div0.o(.text)
    __aeabi_idivmod$div0                     0x000001a1   Thumb Code    74  idiv_div0.o(.text)
    __aeabi_uldivmod                         0x000001f1   Thumb Code    96  uldiv.o(.text)
    __I$use$fp                               0x00000251   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x00000251   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x00000399   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x000003a5   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x000003b5   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x00000485   Thumb Code   234  ddiv.o(.text)
    __aeabi_d2ulz                            0x00000575   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x000005b5   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x000005e1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x000005e1   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x00000605   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x00000605   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x00000625   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x00000625   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x00000647   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x00000647   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x0000066d   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x00000687   Thumb Code   164  depilogue.o(.text)
    DL_Common_delayCycles                    0x0000072b   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_SPI_init                              0x00000735   Thumb Code    60  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_setClockConfig                    0x00000779   Thumb Code    18  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_TimerA_initPWMMode                    0x0000078d   Thumb Code   244  dl_timer.o(.text.DL_TimerA_initPWMMode)
    DL_Timer_initPWMMode                     0x00000881   Thumb Code   172  dl_timer.o(.text.DL_Timer_initPWMMode)
    DL_Timer_initTimerMode                   0x00000941   Thumb Code   220  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00000a29   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00000a45   Thumb Code    20  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00000a5d   Thumb Code    12  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00000a6d   Thumb Code    24  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00000a89   Thumb Code    64  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00000ad1   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    Emm_V5_En_Control                        0x00000ae5   Thumb Code   112  emm_v5.o(.text.Emm_V5_En_Control)
    Emm_V5_Stop_Now                          0x00000b55   Thumb Code   108  emm_v5.o(.text.Emm_V5_Stop_Now)
    Encoder_init                             0x00000bc5   Thumb Code    12  enconder.o(.text.Encoder_init)
    GPIO_WriteBit                            0x00000bd9   Thumb Code    24  led_key.o(.text.GPIO_WriteBit)
    GROUP1_IRQHandler                        0x00000bf5   Thumb Code    48  enconder.o(.text.GROUP1_IRQHandler)
    Get_Encoder                              0x00000c2d   Thumb Code    20  enconder.o(.text.Get_Encoder)
    IIC_XIE                                  0x00000c45   Thumb Code   320  oled.o(.text.IIC_XIE)
    IIC_XIE_DATA                             0x00000d85   Thumb Code   176  oled.o(.text.IIC_XIE_DATA)
    IIC_XIE_ML                               0x00000e35   Thumb Code   176  oled.o(.text.IIC_XIE_ML)
    Line_Control                             0x00000ee9   Thumb Code   500  control.o(.text.Line_Control)
    MaixCam_Init                             0x000010f5   Thumb Code    16  maixcam.o(.text.MaixCam_Init)
    Motor_Write                              0x0000110d   Thumb Code   140  motor.o(.text.Motor_Write)
    OLED_Clear                               0x000011ad   Thumb Code   224  oled.o(.text.OLED_Clear)
    OLED_Init                                0x00001291   Thumb Code   260  oled.o(.text.OLED_Init)
    OLED_ShowChar                            0x00001399   Thumb Code   244  oled.o(.text.OLED_ShowChar)
    OLED_Write                               0x00001495   Thumb Code   140  oled.o(.text.OLED_Write)
    OLED_task                                0x00001525   Thumb Code   120  startuptask.o(.text.OLED_task)
    PID_Init                                 0x000015ad   Thumb Code    24  control.o(.text.PID_Init)
    PendSV_Handler                           0x000015e1   Thumb Code    68  port.o(.text.PendSV_Handler)
    RGB_task                                 0x00001625   Thumb Code    16  startuptask.o(.text.RGB_task)
    SVC_Handler                              0x00001635   Thumb Code     2  port.o(.text.SVC_Handler)
    SYSCFG_DL_GPIO_init                      0x00001639   Thumb Code   184  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_MOTOR_PWM_init                 0x00001719   Thumb Code   120  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    SYSCFG_DL_SPI_WS2812_init                0x000017a9   Thumb Code    56  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    SYSCFG_DL_SYSCTL_init                    0x000017f5   Thumb Code    68  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00001845   Thumb Code     2  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_Servo_init                     0x00001849   Thumb Code   188  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    SYSCFG_DL_TIMER_0_init                   0x0000191d   Thumb Code    48  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_UART_1_init                    0x00001965   Thumb Code    88  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    SYSCFG_DL_ZDT1_init                      0x000019d1   Thumb Code   104  ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init)
    SYSCFG_DL_init                           0x00001a55   Thumb Code    64  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00001aa1   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    StartupTask                              0x00001b11   Thumb Code    92  startuptask.o(.text.StartupTask)
    SysTick_Handler                          0x00001b9d   Thumb Code    32  port.o(.text.SysTick_Handler)
    UART1_IRQHandler                         0x00001bc1   Thumb Code   124  maixcam.o(.text.UART1_IRQHandler)
    ZDT_Init                                 0x00001c49   Thumb Code    12  emm_v5.o(.text.ZDT_Init)
    delay_noOS                               0x00001c59   Thumb Code   120  delay.o(.text.delay_noOS)
    line_task                                0x00001cd5   Thumb Code    28  startuptask.o(.text.line_task)
    main                                     0x00001cf5   Thumb Code    58  main.o(.text.main)
    main_task                                0x00001d31   Thumb Code   120  startuptask.o(.text.main_task)
    pvPortMalloc                             0x000021bd   Thumb Code   372  heap_4.o(.text.pvPortMalloc)
    pxPortInitialiseStack                    0x00002335   Thumb Code    32  port.o(.text.pxPortInitialiseStack)
    ulSetInterruptMaskFromISR                0x00002359   Thumb Code     8  port.o(.text.ulSetInterruptMaskFromISR)
    uxListRemove                             0x00002361   Thumb Code    34  list.o(.text.uxListRemove)
    vClearInterruptMaskFromISR               0x00002383   Thumb Code     6  port.o(.text.vClearInterruptMaskFromISR)
    vListInitialise                          0x00002389   Thumb Code    20  list.o(.text.vListInitialise)
    vListInitialiseItem                      0x0000239d   Thumb Code     6  list.o(.text.vListInitialiseItem)
    vListInsert                              0x000023a3   Thumb Code    60  list.o(.text.vListInsert)
    vPortEnterCritical                       0x000023e1   Thumb Code    20  port.o(.text.vPortEnterCritical)
    vPortExitCritical                        0x000023f5   Thumb Code    24  port.o(.text.vPortExitCritical)
    vPortFree                                0x00002411   Thumb Code   148  heap_4.o(.text.vPortFree)
    vPortSetupTimerInterrupt                 0x000024a9   Thumb Code    20  port.o(.text.vPortSetupTimerInterrupt)
    vPortYield                               0x00002505   Thumb Code    20  port.o(.text.vPortYield)
    vQueueAddToRegistry                      0x0000251d   Thumb Code   244  queue.o(.text.vQueueAddToRegistry)
    vQueueWaitForMessageRestricted           0x00002615   Thumb Code    92  queue.o(.text.vQueueWaitForMessageRestricted)
    vTaskDelay                               0x00002671   Thumb Code   116  tasks.o(.text.vTaskDelay)
    vTaskInternalSetTimeOutState             0x000026e5   Thumb Code    12  tasks.o(.text.vTaskInternalSetTimeOutState)
    vTaskMissedYield                         0x000026f1   Thumb Code     8  tasks.o(.text.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x000026f9   Thumb Code   128  tasks.o(.text.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x00002779   Thumb Code   160  tasks.o(.text.vTaskPlaceOnEventListRestricted)
    vTaskStartScheduler                      0x00002819   Thumb Code    72  tasks.o(.text.vTaskStartScheduler)
    vTaskSuspendAll                          0x00002875   Thumb Code    12  tasks.o(.text.vTaskSuspendAll)
    vTaskSwitchContext                       0x00002881   Thumb Code   136  tasks.o(.text.vTaskSwitchContext)
    xPortStartScheduler                      0x00002915   Thumb Code    84  port.o(.text.xPortStartScheduler)
    xQueueGenericCreate                      0x00002971   Thumb Code   150  queue.o(.text.xQueueGenericCreate)
    xQueueReceive                            0x00002a07   Thumb Code   322  queue.o(.text.xQueueReceive)
    xTaskCheckForTimeOut                     0x00002b49   Thumb Code   116  tasks.o(.text.xTaskCheckForTimeOut)
    xTaskCreate                              0x00002bc1   Thumb Code  1068  tasks.o(.text.xTaskCreate)
    xTaskGetSchedulerState                   0x00003001   Thumb Code    24  tasks.o(.text.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x00003019   Thumb Code     8  tasks.o(.text.xTaskGetTickCount)
    xTaskIncrementTick                       0x00003025   Thumb Code   304  tasks.o(.text.xTaskIncrementTick)
    xTaskRemoveFromEventList                 0x00003155   Thumb Code   172  tasks.o(.text.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x00003201   Thumb Code   264  tasks.o(.text.xTaskResumeAll)
    xTimerCreateTimerTask                    0x00003315   Thumb Code   108  timers.o(.text.xTimerCreateTimerTask)
    __0vsprintf                              0x00003395   Thumb Code    30  printfa.o(i.__0vsprintf)
    __1vsprintf                              0x00003395   Thumb Code     0  printfa.o(i.__0vsprintf)
    __2vsprintf                              0x00003395   Thumb Code     0  printfa.o(i.__0vsprintf)
    __c89vsprintf                            0x00003395   Thumb Code     0  printfa.o(i.__0vsprintf)
    vsprintf                                 0x00003395   Thumb Code     0  printfa.o(i.__0vsprintf)
    __ARM_clz                                0x000033b9   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __scatterload_copy                       0x000033e9   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x000033f9   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00003401   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    F6x8                                     0x00003cc6   Data         552  oled.o(.rodata.F6x8)
    F8X16                                    0x00003eee   Data        1520  oled.o(.rodata.F8X16)
    uxTopUsedPriority                        0x0000453c   Data           4  tasks.o(.rodata.uxTopUsedPriority)
    Region$$Table$$Base                      0x00004540   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00004560   Number         0  anon$$obj.o(Region$$Table)
    Line_EN                                  0x20200000   Data           4  startuptask.o(.data.Line_EN)
    OLED_Lock                                0x20200004   Data           4  oled.o(.data.OLED_Lock)
    Speed1_Temp                              0x202000ec   Data           2  enconder.o(.bss..L_MergedGlobals)
    Speed2_Temp                              0x202000ee   Data           2  enconder.o(.bss..L_MergedGlobals)
    Serx                                     0x202000f0   Data          44  control.o(.bss..L_MergedGlobals)
    Sery                                     0x2020011c   Data          44  control.o(.bss..L_MergedGlobals)
    JiGuang                                  0x2020014a   Data           4  maixcam.o(.bss..L_MergedGlobals)
    Camera_fps                               0x20200150   Data           4  maixcam.o(.bss..L_MergedGlobals)
    Camera_flag                              0x20200154   Data           4  maixcam.o(.bss..L_MergedGlobals)
    Line                                     0x202001a8   Data           8  control.o(.bss.Line)
    Start_flag                               0x202001b0   Data           4  startuptask.o(.bss.Start_flag)
    distance                                 0x202001b4   Data           4  startuptask.o(.bss.distance)
    gSPI_WS2812Backup                        0x202001b8   Data          40  ti_msp_dl_config.o(.bss.gSPI_WS2812Backup)
    gServoBackup                             0x202001e0   Data         188  ti_msp_dl_config.o(.bss.gServoBackup)
    gTIMER_0Backup                           0x2020029c   Data         120  ti_msp_dl_config.o(.bss.gTIMER_0Backup)
    pxCurrentTCB                             0x20200314   Data           4  tasks.o(.bss.pxCurrentTCB)
    xQueueRegistry                           0x20205778   Data          64  queue.o(.bss.xQueueRegistry)
    __initial_sp                             0x20205fb8   Data           0  startup_mspm0g350x_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00004570, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00004560, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO          834    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000000   Code   RO         1427  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO         1484    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO         1487    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         1489    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         1491    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO         1492    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         1494    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         1496    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO         1485    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x00000014   Code   RO          835    .text               startup_mspm0g350x_uvision.o
    0x000000e8   0x000000e8   0x00000030   Code   RO         1438    .text               mc_p.l(llmul.o)
    0x00000118   0x00000118   0x00000024   Code   RO         1440    .text               mc_p.l(memcpya.o)
    0x0000013c   0x0000013c   0x00000024   Code   RO         1442    .text               mc_p.l(memseta.o)
    0x00000160   0x00000160   0x0000003e   Code   RO         1503    .text               mc_p.l(uidiv_div0.o)
    0x0000019e   0x0000019e   0x00000002   PAD
    0x000001a0   0x000001a0   0x00000050   Code   RO         1505    .text               mc_p.l(idiv_div0.o)
    0x000001f0   0x000001f0   0x00000060   Code   RO         1509    .text               mc_p.l(uldiv.o)
    0x00000250   0x00000250   0x00000000   Code   RO         1511    .text               mc_p.l(iusefp.o)
    0x00000250   0x00000250   0x00000164   Code   RO         1514    .text               mf_p.l(dadd.o)
    0x000003b4   0x000003b4   0x000000d0   Code   RO         1516    .text               mf_p.l(dmul.o)
    0x00000484   0x00000484   0x000000f0   Code   RO         1518    .text               mf_p.l(ddiv.o)
    0x00000574   0x00000574   0x00000040   Code   RO         1520    .text               mf_p.l(dfixul.o)
    0x000005b4   0x000005b4   0x00000028   Code   RO         1522    .text               mf_p.l(cdrcmple.o)
    0x000005dc   0x000005dc   0x00000004   PAD
    0x000005e0   0x000005e0   0x00000024   Code   RO         1524    .text               mc_p.l(init.o)
    0x00000604   0x00000604   0x00000020   Code   RO         1526    .text               mc_p.l(llshl.o)
    0x00000624   0x00000624   0x00000022   Code   RO         1528    .text               mc_p.l(llushr.o)
    0x00000646   0x00000646   0x00000026   Code   RO         1530    .text               mc_p.l(llsshr.o)
    0x0000066c   0x0000066c   0x000000be   Code   RO         1532    .text               mf_p.l(depilogue.o)
    0x0000072a   0x0000072a   0x0000000a   Code   RO           51    .text.DL_Common_delayCycles  dl_common.o
    0x00000734   0x00000734   0x00000044   Code   RO          511    .text.DL_SPI_init   dl_spi.o
    0x00000778   0x00000778   0x00000012   Code   RO          513    .text.DL_SPI_setClockConfig  dl_spi.o
    0x0000078a   0x0000078a   0x00000002   PAD
    0x0000078c   0x0000078c   0x000000f4   Code   RO          643    .text.DL_TimerA_initPWMMode  dl_timer.o
    0x00000880   0x00000880   0x000000c0   Code   RO          587    .text.DL_Timer_initPWMMode  dl_timer.o
    0x00000940   0x00000940   0x000000e8   Code   RO          569    .text.DL_Timer_initTimerMode  dl_timer.o
    0x00000a28   0x00000a28   0x0000001c   Code   RO          617    .text.DL_Timer_setCaptCompUpdateMethod  dl_timer.o
    0x00000a44   0x00000a44   0x00000018   Code   RO          591    .text.DL_Timer_setCaptureCompareOutCtl  dl_timer.o
    0x00000a5c   0x00000a5c   0x00000010   Code   RO          571    .text.DL_Timer_setCaptureCompareValue  dl_timer.o
    0x00000a6c   0x00000a6c   0x0000001c   Code   RO          565    .text.DL_Timer_setClockConfig  dl_timer.o
    0x00000a88   0x00000a88   0x00000048   Code   RO          676    .text.DL_UART_init  dl_uart.o
    0x00000ad0   0x00000ad0   0x00000012   Code   RO          678    .text.DL_UART_setClockConfig  dl_uart.o
    0x00000ae2   0x00000ae2   0x00000002   PAD
    0x00000ae4   0x00000ae4   0x00000070   Code   RO         1237    .text.Emm_V5_En_Control  emm_v5.o
    0x00000b54   0x00000b54   0x00000070   Code   RO         1241    .text.Emm_V5_Stop_Now  emm_v5.o
    0x00000bc4   0x00000bc4   0x00000014   Code   RO         1360    .text.Encoder_init  enconder.o
    0x00000bd8   0x00000bd8   0x0000001c   Code   RO         1349    .text.GPIO_WriteBit  led_key.o
    0x00000bf4   0x00000bf4   0x00000038   Code   RO         1362    .text.GROUP1_IRQHandler  enconder.o
    0x00000c2c   0x00000c2c   0x00000018   Code   RO         1358    .text.Get_Encoder   enconder.o
    0x00000c44   0x00000c44   0x00000140   Code   RO         1300    .text.IIC_XIE       oled.o
    0x00000d84   0x00000d84   0x000000b0   Code   RO         1304    .text.IIC_XIE_DATA  oled.o
    0x00000e34   0x00000e34   0x000000b4   Code   RO         1302    .text.IIC_XIE_ML    oled.o
    0x00000ee8   0x00000ee8   0x0000020c   Code   RO         1389    .text.Line_Control  control.o
    0x000010f4   0x000010f4   0x00000018   Code   RO         1413    .text.MaixCam_Init  maixcam.o
    0x0000110c   0x0000110c   0x000000a0   Code   RO         1339    .text.Motor_Write   motor.o
    0x000011ac   0x000011ac   0x000000e4   Code   RO         1308    .text.OLED_Clear    oled.o
    0x00001290   0x00001290   0x00000108   Code   RO         1320    .text.OLED_Init     oled.o
    0x00001398   0x00001398   0x000000fc   Code   RO         1310    .text.OLED_ShowChar  oled.o
    0x00001494   0x00001494   0x00000090   Code   RO         1322    .text.OLED_Write    oled.o
    0x00001524   0x00001524   0x00000088   Code   RO         1190    .text.OLED_task     startuptask.o
    0x000015ac   0x000015ac   0x00000028   Code   RO         1383    .text.PID_Init      control.o
    0x000015d4   0x000015d4   0x0000000c   PAD
    0x000015e0   0x000015e0   0x00000044   Code   RO         1173    .text.PendSV_Handler  port.o
    0x00001624   0x00001624   0x00000010   Code   RO         1188    .text.RGB_task      startuptask.o
    0x00001634   0x00001634   0x00000002   Code   RO         1153    .text.SVC_Handler   port.o
    0x00001636   0x00001636   0x00000002   PAD
    0x00001638   0x00001638   0x000000e0   Code   RO          788    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00001718   0x00001718   0x00000090   Code   RO          792    .text.SYSCFG_DL_MOTOR_PWM_init  ti_msp_dl_config.o
    0x000017a8   0x000017a8   0x0000004c   Code   RO          802    .text.SYSCFG_DL_SPI_WS2812_init  ti_msp_dl_config.o
    0x000017f4   0x000017f4   0x00000050   Code   RO          790    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00001844   0x00001844   0x00000002   Code   RO          804    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00001846   0x00001846   0x00000002   PAD
    0x00001848   0x00001848   0x000000d4   Code   RO          794    .text.SYSCFG_DL_Servo_init  ti_msp_dl_config.o
    0x0000191c   0x0000191c   0x00000048   Code   RO          796    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00001964   0x00001964   0x0000006c   Code   RO          800    .text.SYSCFG_DL_UART_1_init  ti_msp_dl_config.o
    0x000019d0   0x000019d0   0x00000084   Code   RO          798    .text.SYSCFG_DL_ZDT1_init  ti_msp_dl_config.o
    0x00001a54   0x00001a54   0x0000004c   Code   RO          784    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00001aa0   0x00001aa0   0x00000070   Code   RO          786    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00001b10   0x00001b10   0x0000008c   Code   RO         1186    .text.StartupTask   startuptask.o
    0x00001b9c   0x00001b9c   0x00000024   Code   RO         1175    .text.SysTick_Handler  port.o
    0x00001bc0   0x00001bc0   0x00000088   Code   RO         1417    .text.UART1_IRQHandler  maixcam.o
    0x00001c48   0x00001c48   0x00000010   Code   RO         1217    .text.ZDT_Init      emm_v5.o
    0x00001c58   0x00001c58   0x0000007c   Code   RO         1373    .text.delay_noOS    delay.o
    0x00001cd4   0x00001cd4   0x00000020   Code   RO         1194    .text.line_task     startuptask.o
    0x00001cf4   0x00001cf4   0x0000003a   Code   RO          775    .text.main          main.o
    0x00001d2e   0x00001d2e   0x00000002   PAD
    0x00001d30   0x00001d30   0x0000008c   Code   RO         1192    .text.main_task     startuptask.o
    0x00001dbc   0x00001dbc   0x0000004c   Code   RO          994    .text.prvIdleTask   tasks.o
    0x00001e08   0x00001e08   0x00000034   Code   RO         1151    .text.prvTaskExitError  port.o
    0x00001e3c   0x00001e3c   0x00000300   Code   RO         1081    .text.prvTimerTask  timers.o
    0x0000213c   0x0000213c   0x0000007e   Code   RO          915    .text.prvUnlockQueue  queue.o
    0x000021ba   0x000021ba   0x00000002   PAD
    0x000021bc   0x000021bc   0x00000178   Code   RO         1125    .text.pvPortMalloc  heap_4.o
    0x00002334   0x00002334   0x00000024   Code   RO         1149    .text.pxPortInitialiseStack  port.o
    0x00002358   0x00002358   0x00000008   Code   RO         1169    .text.ulSetInterruptMaskFromISR  port.o
    0x00002360   0x00002360   0x00000022   Code   RO          885    .text.uxListRemove  list.o
    0x00002382   0x00002382   0x00000006   Code   RO         1171    .text.vClearInterruptMaskFromISR  port.o
    0x00002388   0x00002388   0x00000014   Code   RO          877    .text.vListInitialise  list.o
    0x0000239c   0x0000239c   0x00000006   Code   RO          879    .text.vListInitialiseItem  list.o
    0x000023a2   0x000023a2   0x0000003c   Code   RO          883    .text.vListInsert   list.o
    0x000023de   0x000023de   0x00000002   PAD
    0x000023e0   0x000023e0   0x00000014   Code   RO         1165    .text.vPortEnterCritical  port.o
    0x000023f4   0x000023f4   0x0000001c   Code   RO         1167    .text.vPortExitCritical  port.o
    0x00002410   0x00002410   0x00000098   Code   RO         1127    .text.vPortFree     heap_4.o
    0x000024a8   0x000024a8   0x0000001c   Code   RO         1157    .text.vPortSetupTimerInterrupt  port.o
    0x000024c4   0x000024c4   0x0000000c   PAD
    0x000024d0   0x000024d0   0x00000034   Code   RO         1159    .text.vPortStartFirstTask  port.o
    0x00002504   0x00002504   0x00000018   Code   RO         1163    .text.vPortYield    port.o
    0x0000251c   0x0000251c   0x000000f8   Code   RO          949    .text.vQueueAddToRegistry  queue.o
    0x00002614   0x00002614   0x0000005c   Code   RO          953    .text.vQueueWaitForMessageRestricted  queue.o
    0x00002670   0x00002670   0x00000074   Code   RO          974    .text.vTaskDelay    tasks.o
    0x000026e4   0x000026e4   0x0000000c   Code   RO         1024    .text.vTaskInternalSetTimeOutState  tasks.o
    0x000026f0   0x000026f0   0x00000008   Code   RO         1028    .text.vTaskMissedYield  tasks.o
    0x000026f8   0x000026f8   0x00000080   Code   RO         1012    .text.vTaskPlaceOnEventList  tasks.o
    0x00002778   0x00002778   0x000000a0   Code   RO         1016    .text.vTaskPlaceOnEventListRestricted  tasks.o
    0x00002818   0x00002818   0x0000005c   Code   RO          992    .text.vTaskStartScheduler  tasks.o
    0x00002874   0x00002874   0x0000000c   Code   RO          970    .text.vTaskSuspendAll  tasks.o
    0x00002880   0x00002880   0x00000094   Code   RO          986    .text.vTaskSwitchContext  tasks.o
    0x00002914   0x00002914   0x0000005c   Code   RO         1155    .text.xPortStartScheduler  port.o
    0x00002970   0x00002970   0x00000096   Code   RO          897    .text.xQueueGenericCreate  queue.o
    0x00002a06   0x00002a06   0x00000142   Code   RO          921    .text.xQueueReceive  queue.o
    0x00002b48   0x00002b48   0x00000078   Code   RO         1026    .text.xTaskCheckForTimeOut  tasks.o
    0x00002bc0   0x00002bc0   0x00000440   Code   RO          964    .text.xTaskCreate   tasks.o
    0x00003000   0x00003000   0x00000018   Code   RO         1040    .text.xTaskGetSchedulerState  tasks.o
    0x00003018   0x00003018   0x0000000c   Code   RO         1000    .text.xTaskGetTickCount  tasks.o
    0x00003024   0x00003024   0x00000130   Code   RO          998    .text.xTaskIncrementTick  tasks.o
    0x00003154   0x00003154   0x000000ac   Code   RO         1018    .text.xTaskRemoveFromEventList  tasks.o
    0x00003200   0x00003200   0x00000114   Code   RO          972    .text.xTaskResumeAll  tasks.o
    0x00003314   0x00003314   0x00000080   Code   RO         1079    .text.xTimerCreateTimerTask  timers.o
    0x00003394   0x00003394   0x00000024   Code   RO         1453    i.__0vsprintf       mc_p.l(printfa.o)
    0x000033b8   0x000033b8   0x0000002e   Code   RO         1534    i.__ARM_clz         mf_p.l(depilogue.o)
    0x000033e6   0x000033e6   0x00000002   PAD
    0x000033e8   0x000033e8   0x0000000e   Code   RO         1538    i.__scatterload_copy  mc_p.l(handlers.o)
    0x000033f6   0x000033f6   0x00000002   PAD
    0x000033f8   0x000033f8   0x00000002   Code   RO         1539    i.__scatterload_null  mc_p.l(handlers.o)
    0x000033fa   0x000033fa   0x00000006   PAD
    0x00003400   0x00003400   0x0000000e   Code   RO         1540    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x0000340e   0x0000340e   0x00000002   PAD
    0x00003410   0x00003410   0x00000174   Code   RO         1454    i._fp_digits        mc_p.l(printfa.o)
    0x00003584   0x00003584   0x000006ec   Code   RO         1455    i._printf_core      mc_p.l(printfa.o)
    0x00003c70   0x00003c70   0x00000020   Code   RO         1456    i._printf_post_padding  mc_p.l(printfa.o)
    0x00003c90   0x00003c90   0x0000002c   Code   RO         1457    i._printf_pre_padding  mc_p.l(printfa.o)
    0x00003cbc   0x00003cbc   0x0000000a   Code   RO         1459    i._sputc            mc_p.l(printfa.o)
    0x00003cc6   0x00003cc6   0x00000228   Data   RO         1324    .rodata.F6x8        oled.o
    0x00003eee   0x00003eee   0x000005f0   Data   RO         1325    .rodata.F8X16       oled.o
    0x000044de   0x000044de   0x00000003   Data   RO          813    .rodata.gMOTOR_PWMClockConfig  ti_msp_dl_config.o
    0x000044e1   0x000044e1   0x00000003   PAD
    0x000044e4   0x000044e4   0x00000008   Data   RO          814    .rodata.gMOTOR_PWMConfig  ti_msp_dl_config.o
    0x000044ec   0x000044ec   0x00000002   Data   RO          823    .rodata.gSPI_WS2812_clockConfig  ti_msp_dl_config.o
    0x000044ee   0x000044ee   0x0000000a   Data   RO          824    .rodata.gSPI_WS2812_config  ti_msp_dl_config.o
    0x000044f8   0x000044f8   0x00000003   Data   RO          815    .rodata.gServoClockConfig  ti_msp_dl_config.o
    0x000044fb   0x000044fb   0x00000001   PAD
    0x000044fc   0x000044fc   0x00000008   Data   RO          816    .rodata.gServoConfig  ti_msp_dl_config.o
    0x00004504   0x00004504   0x00000003   Data   RO          817    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x00004507   0x00004507   0x00000001   PAD
    0x00004508   0x00004508   0x00000014   Data   RO          818    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x0000451c   0x0000451c   0x00000002   Data   RO          821    .rodata.gUART_1ClockConfig  ti_msp_dl_config.o
    0x0000451e   0x0000451e   0x0000000a   Data   RO          822    .rodata.gUART_1Config  ti_msp_dl_config.o
    0x00004528   0x00004528   0x00000002   Data   RO          819    .rodata.gZDT1ClockConfig  ti_msp_dl_config.o
    0x0000452a   0x0000452a   0x0000000a   Data   RO          820    .rodata.gZDT1Config  ti_msp_dl_config.o
    0x00004534   0x00004534   0x00000005   Data   RO         1115    .rodata.str1.1      timers.o
    0x00004539   0x00004539   0x00000003   PAD
    0x0000453c   0x0000453c   0x00000004   Data   RO         1067    .rodata.uxTopUsedPriority  tasks.o
    0x00004540   0x00004540   0x00000020   Data   RO         1537    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00004560, Size: 0x00005fb8, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00004560   0x00000004   Data   RW         1205    .data.Line_EN       startuptask.o
    0x20200004   0x00004564   0x00000004   Data   RW         1328    .data.OLED_Lock     oled.o
    0x20200008   0x00004568   0x00000004   Data   RW         1177    .data.uxCriticalNesting  port.o
    0x2020000c   0x0000456c   0x00000004   PAD
    0x20200010        -       0x00000074   Zero   RW         1069    .bss..L_MergedGlobals  tasks.o
    0x20200084        -       0x0000003c   Zero   RW         1116    .bss..L_MergedGlobals  timers.o
    0x202000c0        -       0x0000001c   Zero   RW         1140    .bss..L_MergedGlobals  heap_4.o
    0x202000dc        -       0x00000010   Zero   RW         1208    .bss..L_MergedGlobals  startuptask.o
    0x202000ec        -       0x00000004   Zero   RW         1364    .bss..L_MergedGlobals  enconder.o
    0x202000f0        -       0x00000058   Zero   RW         1396    .bss..L_MergedGlobals  control.o
    0x20200148        -       0x00000038   Zero   RW         1419    .bss..L_MergedGlobals  maixcam.o
    0x20200180        -       0x00000028   Zero   RW         1070    .bss..L_MergedGlobals.1  tasks.o
    0x202001a8        -       0x00000008   Zero   RW         1395    .bss.Line           control.o
    0x202001b0        -       0x00000004   Zero   RW         1206    .bss.Start_flag     startuptask.o
    0x202001b4        -       0x00000004   Zero   RW         1207    .bss.distance       startuptask.o
    0x202001b8        -       0x00000028   Zero   RW          812    .bss.gSPI_WS2812Backup  ti_msp_dl_config.o
    0x202001e0        -       0x000000bc   Zero   RW          810    .bss.gServoBackup   ti_msp_dl_config.o
    0x2020029c        -       0x00000078   Zero   RW          811    .bss.gTIMER_0Backup  ti_msp_dl_config.o
    0x20200314        -       0x00000004   Zero   RW         1066    .bss.pxCurrentTCB   tasks.o
    0x20200318        -       0x00000460   Zero   RW         1068    .bss.pxReadyTasksLists  tasks.o
    0x20200778        -       0x00005000   Zero   RW         1139    .bss.ucHeap         heap_4.o
    0x20205778        -       0x00000040   Zero   RW          955    .bss.xQueueRegistry  queue.o
    0x202057b8        -       0x00000800   Zero   RW          832    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       564         40          0          0         96       7843   control.o
       124          4          0          0          0       1346   delay.o
        10          0          0          0          0        594   dl_common.o
        86          8          0          0          0      15588   dl_spi.o
       764        188          0          0          0      37597   dl_timer.o
        90          8          0          0          0      14399   dl_uart.o
       240          8          0          0          0      14672   emm_v5.o
       100         20          0          0          4       5588   enconder.o
       528          8          0          0      20508       5119   heap_4.o
        28          4          0          0          0       3385   led_key.o
       120          0          0          0          0       2145   list.o
        58          0          0          0          0        378   main.o
       160         20          0          0         56       8760   maixcam.o
       160         20          0          0          0       6968   motor.o
      1564         24       2072          4          0      11976   oled.o
       452         44          0          4          0       2767   port.o
       938          4          0          0         64      22903   queue.o
        20          4        192          0       2048        744   startup_mspm0g350x_uvision.o
       464         88          0          4         24       5628   startuptask.o
      2748         80          4          0       1280      40549   tasks.o
      1238        244         81          0        348      37239   ti_msp_dl_config.o
       896         34          5          0         60      14208   timers.o

    ----------------------------------------------------------------------
     11390        <USER>       <GROUP>         12      24492     260396   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        38         24          8          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        80          6          0          0          0         72   idiv_div0.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        48          0          0          0          0         72   llmul.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0         60   memcpya.o
        36          0          0          0          0        100   memseta.o
      2266         96          0          0          0        460   printfa.o
        62          0          0          0          0         72   uidiv_div0.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        64         10          0          0          0         68   dfixul.o
       208          6          0          0          0         88   dmul.o

    ----------------------------------------------------------------------
      3976        <USER>          <GROUP>          0          0       1856   Library Totals
        18          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2814        118          0          0          0       1192   mc_p.l
      1144         28          0          0          0        664   mf_p.l

    ----------------------------------------------------------------------
      3976        <USER>          <GROUP>          0          0       1856   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     15366       1024       2394         12      24492     261072   Grand Totals
     15366       1024       2394         12      24492     261072   ELF Image Totals
     15366       1024       2394         12          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                17760 (  17.34kB)
    Total RW  Size (RW Data + ZI Data)             24504 (  23.93kB)
    Total ROM Size (Code + RO Data + RW Data)      17772 (  17.36kB)

==============================================================================

