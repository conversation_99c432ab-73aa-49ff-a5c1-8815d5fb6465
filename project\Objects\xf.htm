<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\xf.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\xf.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6190004: Last Updated: Mon Jul 28 17:40:51 2025
<BR><P>
<H3>Maximum Stack Usage =        212 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[a]">ADC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[b]">ADC1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1b]">AES_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[c]">CANFD0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[d]">DAC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1d]">DMA_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[6]">GROUP0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[7]">GROUP1_IRQHandler</a> from enconder.o(.text.GROUP1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[19]">I2C0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1a]">I2C1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[21]">OLED_task</a> from startuptask.o(.text.OLED_task) referenced from startuptask.o(.text.StartupTask)
 <LI><a href="#[4]">PendSV_Handler</a> from port.o(.text.PendSV_Handler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[20]">RGB_task</a> from startuptask.o(.text.RGB_task) referenced from startuptask.o(.text.StartupTask)
 <LI><a href="#[1c]">RTC_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[e]">SPI0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[f]">SPI1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[3]">SVC_Handler</a> from port.o(.text.SVC_Handler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[5]">SysTick_Handler</a> from port.o(.text.SysTick_Handler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[15]">TIMA0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[16]">TIMA1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[13]">TIMG0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[18]">TIMG12_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[14]">TIMG6_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[17]">TIMG7_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[8]">TIMG8_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[12]">UART0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[10]">UART1_IRQHandler</a> from maixcam.o(.text.UART1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[11]">UART2_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[9]">UART3_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1f]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_mspm0g350x_uvision.o(.text)
 <LI><a href="#[27]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0vsprintf)
 <LI><a href="#[23]">line_task</a> from startuptask.o(.text.line_task) referenced from startuptask.o(.text.StartupTask)
 <LI><a href="#[1e]">main</a> from main.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[22]">main_task</a> from startuptask.o(.text.main_task) referenced from startuptask.o(.text.StartupTask)
 <LI><a href="#[25]">prvIdleTask</a> from tasks.o(.text.prvIdleTask) referenced from tasks.o(.text.vTaskStartScheduler)
 <LI><a href="#[24]">prvTaskExitError</a> from port.o(.text.prvTaskExitError) referenced from port.o(.text.pxPortInitialiseStack)
 <LI><a href="#[26]">prvTimerTask</a> from timers.o(.text.prvTimerTask) referenced from timers.o(.text.xTimerCreateTimerTask)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[1f]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(.text)
</UL>
<P><STRONG><a name="[a2]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[29]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[42]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[a3]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[a4]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[a5]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[a6]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[a7]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>ADC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>ADC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>AES_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>CANFD0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>DAC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>Default_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>

<P><STRONG><a name="[6]"></a>GROUP0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>I2C0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TIMA0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIMA1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TIMG0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIMG12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIMG6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TIMG7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>TIMG8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>UART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>UART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[91]"></a>__aeabi_lmul</STRONG> (Thumb, 48 bytes, Stack size 12 bytes, llmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_lmul
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[a8]"></a>_ll_mul</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, llmul.o(.text), UNUSED)

<P><STRONG><a name="[94]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[a9]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[aa]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[2d]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[97]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[ab]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[2c]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_En_Control
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Stop_Now
</UL>

<P><STRONG><a name="[ac]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[ad]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[2e]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[2f]"></a>__aeabi_fadd</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
</UL>

<P><STRONG><a name="[32]"></a>__aeabi_fsub</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
</UL>

<P><STRONG><a name="[33]"></a>__aeabi_frsub</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>

<P><STRONG><a name="[4c]"></a>__aeabi_fmul</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, fmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
</UL>

<P><STRONG><a name="[34]"></a>__aeabi_fdiv</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, fdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
</UL>

<P><STRONG><a name="[50]"></a>__aeabi_fcmple</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, fcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
</UL>

<P><STRONG><a name="[4e]"></a>__aeabi_fcmplt</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, fcmplt.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
</UL>

<P><STRONG><a name="[51]"></a>__aeabi_fcmpge</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, fcmpge.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
</UL>

<P><STRONG><a name="[4d]"></a>__aeabi_fcmpgt</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, fcmpgt.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
</UL>

<P><STRONG><a name="[35]"></a>__aeabi_ui2f</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, ffltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_ui2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
</UL>

<P><STRONG><a name="[4f]"></a>__aeabi_f2iz</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, ffixi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
</UL>

<P><STRONG><a name="[ae]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, uidiv_div0.o(.text), UNUSED)

<P><STRONG><a name="[a1]"></a>__aeabi_uidivmod</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, uidiv_div0.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[36]"></a>__aeabi_uldivmod</STRONG> (Thumb, 96 bytes, Stack size 48 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[af]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[31]"></a>_float_round</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>

<P><STRONG><a name="[30]"></a>_float_epilogue</STRONG> (Thumb, 114 bytes, Stack size 12 bytes, fepilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>

<P><STRONG><a name="[39]"></a>__aeabi_dadd</STRONG> (Thumb, 328 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[3d]"></a>__aeabi_dsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[3e]"></a>__aeabi_drsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[3f]"></a>__aeabi_dmul</STRONG> (Thumb, 202 bytes, Stack size 72 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[40]"></a>__aeabi_ddiv</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[41]"></a>__aeabi_d2ulz</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[9e]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[2a]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[b0]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[38]"></a>__aeabi_llsl</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[b1]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[37]"></a>__aeabi_llsr</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[b2]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[3a]"></a>__aeabi_lasr</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[b3]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[3c]"></a>_double_round</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[3b]"></a>_double_epilogue</STRONG> (Thumb, 164 bytes, Stack size 48 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_clz
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[6f]"></a>DL_Common_delayCycles</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dl_common.o(.text.DL_Common_delayCycles))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[61]"></a>DL_SPI_init</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, dl_spi.o(.text.DL_SPI_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_SPI_init
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_WS2812_init
</UL>

<P><STRONG><a name="[60]"></a>DL_SPI_setClockConfig</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, dl_spi.o(.text.DL_SPI_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_SPI_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_WS2812_init
</UL>

<P><STRONG><a name="[63]"></a>DL_TimerA_initPWMMode</STRONG> (Thumb, 244 bytes, Stack size 20 bytes, dl_timer.o(.text.DL_TimerA_initPWMMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DL_TimerA_initPWMMode
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_Servo_init
</UL>

<P><STRONG><a name="[5c]"></a>DL_Timer_initPWMMode</STRONG> (Thumb, 172 bytes, Stack size 20 bytes, dl_timer.o(.text.DL_Timer_initPWMMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DL_Timer_initPWMMode
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_MOTOR_PWM_init
</UL>

<P><STRONG><a name="[65]"></a>DL_Timer_initTimerMode</STRONG> (Thumb, 220 bytes, Stack size 16 bytes, dl_timer.o(.text.DL_Timer_initTimerMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Timer_initTimerMode
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
</UL>

<P><STRONG><a name="[5e]"></a>DL_Timer_setCaptCompUpdateMethod</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setCaptCompUpdateMethod
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_Servo_init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_MOTOR_PWM_init
</UL>

<P><STRONG><a name="[5d]"></a>DL_Timer_setCaptureCompareOutCtl</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setCaptureCompareOutCtl
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_Servo_init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_MOTOR_PWM_init
</UL>

<P><STRONG><a name="[52]"></a>DL_Timer_setCaptureCompareValue</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareValue))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Write
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_Servo_init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_MOTOR_PWM_init
</UL>

<P><STRONG><a name="[5b]"></a>DL_Timer_setClockConfig</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_Servo_init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_MOTOR_PWM_init
</UL>

<P><STRONG><a name="[68]"></a>DL_UART_init</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, dl_uart.o(.text.DL_UART_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_UART_init
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ZDT1_init
</UL>

<P><STRONG><a name="[67]"></a>DL_UART_setClockConfig</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, dl_uart.o(.text.DL_UART_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ZDT1_init
</UL>

<P><STRONG><a name="[44]"></a>Emm_V5_En_Control</STRONG> (Thumb, 112 bytes, Stack size 40 bytes, emm_v5.o(.text.Emm_V5_En_Control))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Emm_V5_En_Control
</UL>
<BR>[Calls]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_task
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[45]"></a>Emm_V5_Stop_Now</STRONG> (Thumb, 108 bytes, Stack size 40 bytes, emm_v5.o(.text.Emm_V5_Stop_Now))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Emm_V5_Stop_Now
</UL>
<BR>[Calls]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[77]"></a>Encoder_init</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, enconder.o(.text.Encoder_init))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[47]"></a>GPIO_WriteBit</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, led_key.o(.text.GPIO_WriteBit))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE_DATA
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE_ML
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE
</UL>

<P><STRONG><a name="[7]"></a>GROUP1_IRQHandler</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, enconder.o(.text.GROUP1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GROUP1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>Get_Encoder</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, enconder.o(.text.Get_Encoder))
<BR><BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_task
</UL>

<P><STRONG><a name="[46]"></a>IIC_XIE</STRONG> (Thumb, 320 bytes, Stack size 40 bytes, oled.o(.text.IIC_XIE))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = IIC_XIE
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE_DATA
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE_ML
</UL>

<P><STRONG><a name="[48]"></a>IIC_XIE_DATA</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, oled.o(.text.IIC_XIE_DATA))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = IIC_XIE_DATA &rArr; IIC_XIE
</UL>
<BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[49]"></a>IIC_XIE_ML</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, oled.o(.text.IIC_XIE_ML))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = IIC_XIE_ML &rArr; IIC_XIE
</UL>
<BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[4a]"></a>Line_Control</STRONG> (Thumb, 584 bytes, Stack size 72 bytes, control.o(.text.Line_Control))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = Line_Control &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Write
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmplt
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpgt
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_task
</UL>

<P><STRONG><a name="[75]"></a>MaixCam_Init</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, maixcam.o(.text.MaixCam_Init))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4b]"></a>Motor_Write</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, motor.o(.text.Motor_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Motor_Write
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_task
</UL>

<P><STRONG><a name="[53]"></a>OLED_Clear</STRONG> (Thumb, 224 bytes, Stack size 40 bytes, oled.o(.text.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = OLED_Clear &rArr; IIC_XIE_ML &rArr; IIC_XIE
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE_ML
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[54]"></a>OLED_Init</STRONG> (Thumb, 260 bytes, Stack size 16 bytes, oled.o(.text.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; IIC_XIE_ML &rArr; IIC_XIE
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE_ML
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[55]"></a>OLED_ShowChar</STRONG> (Thumb, 244 bytes, Stack size 32 bytes, oled.o(.text.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = OLED_ShowChar &rArr; IIC_XIE_DATA &rArr; IIC_XIE
</UL>
<BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE_DATA
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_XIE_ML
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write
</UL>

<P><STRONG><a name="[56]"></a>OLED_Write</STRONG> (Thumb, 140 bytes, Stack size 64 bytes, oled.o(.text.OLED_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = OLED_Write &rArr; OLED_ShowChar &rArr; IIC_XIE_DATA &rArr; IIC_XIE
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsprintf
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_task
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_task
</UL>

<P><STRONG><a name="[21]"></a>OLED_task</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, startuptask.o(.text.OLED_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = OLED_task &rArr; OLED_Write &rArr; OLED_ShowChar &rArr; IIC_XIE_DATA &rArr; IIC_XIE
</UL>
<BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Write
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Encoder
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startuptask.o(.text.StartupTask)
</UL>
<P><STRONG><a name="[7a]"></a>PID_Init</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, control.o(.text.PID_Init))
<BR><BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_task
</UL>

<P><STRONG><a name="[4]"></a>PendSV_Handler</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, port.o(.text.PendSV_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = PendSV_Handler &rArr; vTaskSwitchContext
</UL>
<BR>[Calls]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>RGB_task</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, startuptask.o(.text.RGB_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = RGB_task &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startuptask.o(.text.StartupTask)
</UL>
<P><STRONG><a name="[3]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, port.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>SYSCFG_DL_GPIO_init</STRONG> (Thumb, 184 bytes, Stack size 20 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SYSCFG_DL_GPIO_init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[5a]"></a>SYSCFG_DL_MOTOR_PWM_init</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = SYSCFG_DL_MOTOR_PWM_init &rArr; DL_Timer_initPWMMode
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[5f]"></a>SYSCFG_DL_SPI_WS2812_init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_SPI_WS2812_init &rArr; DL_SPI_init
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setClockConfig
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[6d]"></a>SYSCFG_DL_SYSCTL_init</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[6e]"></a>SYSCFG_DL_SYSTICK_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[62]"></a>SYSCFG_DL_Servo_init</STRONG> (Thumb, 188 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = SYSCFG_DL_Servo_init &rArr; DL_TimerA_initPWMMode
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[64]"></a>SYSCFG_DL_TIMER_0_init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_TIMER_0_init &rArr; DL_Timer_initTimerMode
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[66]"></a>SYSCFG_DL_UART_1_init</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_UART_1_init &rArr; DL_UART_init
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[69]"></a>SYSCFG_DL_ZDT1_init</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_ZDT1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SYSCFG_DL_ZDT1_init &rArr; DL_UART_init
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[6a]"></a>SYSCFG_DL_init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SYSCFG_DL_init &rArr; SYSCFG_DL_Servo_init &rArr; DL_TimerA_initPWMMode
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_WS2812_init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ZDT1_init
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_0_init
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_Servo_init
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_MOTOR_PWM_init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6b]"></a>SYSCFG_DL_initPower</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_initPower))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_initPower
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_delayCycles
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[70]"></a>StartupTask</STRONG> (Thumb, 92 bytes, Stack size 32 bytes, startuptask.o(.text.StartupTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = StartupTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5]"></a>SysTick_Handler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, port.o(.text.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SysTick_Handler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vClearInterruptMaskFromISR
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulSetInterruptMaskFromISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>UART1_IRQHandler</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, maixcam.o(.text.UART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>ZDT_Init</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, emm_v5.o(.text.ZDT_Init))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[78]"></a>delay_noOS</STRONG> (Thumb, 120 bytes, Stack size 0 bytes, delay.o(.text.delay_noOS))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[23]"></a>line_task</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, startuptask.o(.text.line_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = line_task &rArr; Line_Control &rArr; __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Line_Control
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startuptask.o(.text.StartupTask)
</UL>
<P><STRONG><a name="[1e]"></a>main</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = main &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartupTask
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_En_Control
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_noOS
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Stop_Now
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encoder_init
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ZDT_Init
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MaixCam_Init
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[22]"></a>main_task</STRONG> (Thumb, 120 bytes, Stack size 0 bytes, startuptask.o(.text.main_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = main_task &rArr; OLED_Write &rArr; OLED_ShowChar &rArr; IIC_XIE_DATA &rArr; IIC_XIE
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Init
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_En_Control
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startuptask.o(.text.StartupTask)
</UL>
<P><STRONG><a name="[89]"></a>pvPortMalloc</STRONG> (Thumb, 372 bytes, Stack size 32 bytes, heap_4.o(.text.pvPortMalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[99]"></a>pxPortInitialiseStack</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, port.o(.text.pxPortInitialiseStack))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[72]"></a>ulSetInterruptMaskFromISR</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, port.o(.text.ulSetInterruptMaskFromISR))
<BR><BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[7c]"></a>uxListRemove</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, list.o(.text.uxListRemove))
<BR><BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
</UL>

<P><STRONG><a name="[74]"></a>vClearInterruptMaskFromISR</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, port.o(.text.vClearInterruptMaskFromISR))
<BR><BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[92]"></a>vListInitialise</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, list.o(.text.vListInitialise))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[98]"></a>vListInitialiseItem</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, list.o(.text.vListInitialiseItem))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[83]"></a>vListInsert</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, list.o(.text.vListInsert))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
</UL>

<P><STRONG><a name="[7b]"></a>vPortEnterCritical</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, port.o(.text.vPortEnterCritical))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>

<P><STRONG><a name="[7d]"></a>vPortExitCritical</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, port.o(.text.vPortExitCritical))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>

<P><STRONG><a name="[7e]"></a>vPortFree</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, heap_4.o(.text.vPortFree))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
</UL>

<P><STRONG><a name="[8e]"></a>vPortSetupTimerInterrupt</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, port.o(.text.vPortSetupTimerInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[7f]"></a>vPortYield</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, port.o(.text.vPortYield))
<BR><BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>

<P><STRONG><a name="[9a]"></a>vQueueAddToRegistry</STRONG> (Thumb, 244 bytes, Stack size 16 bytes, queue.o(.text.vQueueAddToRegistry))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vQueueAddToRegistry
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[84]"></a>vQueueWaitForMessageRestricted</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, queue.o(.text.vQueueWaitForMessageRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = vQueueWaitForMessageRestricted &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[57]"></a>vTaskDelay</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, tasks.o(.text.vTaskDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortYield
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;line_task
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_task
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_task
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RGB_task
</UL>

<P><STRONG><a name="[95]"></a>vTaskInternalSetTimeOutState</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, tasks.o(.text.vTaskInternalSetTimeOutState))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[88]"></a>vTaskMissedYield</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(.text.vTaskMissedYield))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[8b]"></a>vTaskPlaceOnEventList</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, tasks.o(.text.vTaskPlaceOnEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = vTaskPlaceOnEventList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[8a]"></a>vTaskPlaceOnEventListRestricted</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, tasks.o(.text.vTaskPlaceOnEventListRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = vTaskPlaceOnEventListRestricted &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[79]"></a>vTaskStartScheduler</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, tasks.o(.text.vTaskStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[80]"></a>vTaskSuspendAll</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, tasks.o(.text.vTaskSuspendAll))
<BR><BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[28]"></a>vTaskSwitchContext</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, tasks.o(.text.vTaskSwitchContext))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vTaskSwitchContext
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[8d]"></a>xPortStartScheduler</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, port.o(.text.xPortStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = xPortStartScheduler &rArr; vTaskSwitchContext
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSetupTimerInterrupt
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortStartFirstTask
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[90]"></a>xQueueGenericCreate</STRONG> (Thumb, 150 bytes, Stack size 32 bytes, queue.o(.text.xQueueGenericCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lmul
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[85]"></a>xQueueReceive</STRONG> (Thumb, 322 bytes, Stack size 40 bytes, queue.o(.text.xQueueReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = xQueueReceive &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortYield
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[96]"></a>xTaskCheckForTimeOut</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, tasks.o(.text.xTaskCheckForTimeOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xTaskCheckForTimeOut
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[71]"></a>xTaskCreate</STRONG> (Thumb, 1068 bytes, Stack size 80 bytes, tasks.o(.text.xTaskCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxPortInitialiseStack
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialiseItem
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortYield
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset4
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartupTask
</UL>

<P><STRONG><a name="[93]"></a>xTaskGetSchedulerState</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, tasks.o(.text.xTaskGetSchedulerState))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[81]"></a>xTaskGetTickCount</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(.text.xTaskGetTickCount))
<BR><BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[73]"></a>xTaskIncrementTick</STRONG> (Thumb, 304 bytes, Stack size 28 bytes, tasks.o(.text.xTaskIncrementTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>

<P><STRONG><a name="[87]"></a>xTaskRemoveFromEventList</STRONG> (Thumb, 172 bytes, Stack size 20 bytes, tasks.o(.text.xTaskRemoveFromEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = xTaskRemoveFromEventList
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[82]"></a>xTaskResumeAll</STRONG> (Thumb, 264 bytes, Stack size 24 bytes, tasks.o(.text.xTaskResumeAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortYield
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[8c]"></a>xTimerCreateTimerTask</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, timers.o(.text.xTimerCreateTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = xTimerCreateTimerTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[9b]"></a>__0vsprintf</STRONG> (Thumb, 30 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[b4]"></a>__1vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[b5]"></a>__2vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[b6]"></a>__c89vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf), UNUSED)

<P><STRONG><a name="[58]"></a>vsprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write
</UL>

<P><STRONG><a name="[43]"></a>__ARM_clz</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, depilogue.o(i.__ARM_clz), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[b7]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[b8]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[b9]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[86]"></a>prvUnlockQueue</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, queue.o(.text.prvUnlockQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskMissedYield
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[25]"></a>prvIdleTask</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, tasks.o(.text.prvIdleTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = prvIdleTask &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortYield
</UL>
<BR>[Address Reference Count : 1]<UL><LI> tasks.o(.text.vTaskStartScheduler)
</UL>
<P><STRONG><a name="[26]"></a>prvTimerTask</STRONG> (Thumb, 764 bytes, Stack size 32 bytes, timers.o(.text.prvTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = prvTimerTask &rArr; xQueueReceive &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortYield
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(.text.xTimerCreateTimerTask)
</UL>
<P><STRONG><a name="[24]"></a>prvTaskExitError</STRONG> (Thumb, 48 bytes, Stack size 4 bytes, port.o(.text.prvTaskExitError))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = prvTaskExitError
</UL>
<BR>[Address Reference Count : 1]<UL><LI> port.o(.text.pxPortInitialiseStack)
</UL>
<P><STRONG><a name="[8f]"></a>vPortStartFirstTask</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, port.o(.text.vPortStartFirstTask))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[9d]"></a>_fp_digits</STRONG> (Thumb, 344 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[9c]"></a>_printf_core</STRONG> (Thumb, 1754 bytes, Stack size 128 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsprintf
</UL>

<P><STRONG><a name="[a0]"></a>_printf_post_padding</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[9f]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 40 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[27]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0vsprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
