# 8路循迹功能说明

## 功能概述
本项目实现了基于8路红外传感器的智能循迹功能，支持简单模式和高级模式两种控制策略。

## 硬件配置
- **传感器布局**: 8路红外传感器从左到右依次为 HW1-HW8
- **传感器接口**: 
  - HW1-HW6: GPIOB端口
  - HW7-HW8: GPIOB端口(upper组)
- **电机控制**: 双电机差速控制

## 传感器映射
```
传感器编号:  0    1    2    3    4    5    6    7
物理接口:   HW1  HW2  HW3  HW4  HW5  HW6  HW7  HW8
位置:      最左  左   左内  中左  中右  右内  右   最右
```

## 控制模式

### 1. 简单循迹模式 (Line_Control_Simple)
- **特点**: 简单优先级控制，易于理解和调试
- **策略**: 最外侧传感器优先级最高，依次向内递减
- **适用**: 标准赛道，简单路径

**控制逻辑**:
- 最外侧传感器(0,7): 大幅转向 (GAIN * GAIN_K)
- 外侧传感器(1,6): 中等转向 (GAIN)
- 内侧传感器(2,5): 小幅转向 (GAIN/2)
- 中间传感器(3,4): 直行

### 2. 高级循迹模式 (Line_Control)
- **特点**: 智能多传感器融合控制
- **策略**: 根据传感器组合状态和位置权重进行精确控制
- **适用**: 复杂赛道，交叉路口，宽线条

**控制逻辑**:
- 单传感器: 精确转向控制
- 双传感器: 位置权重计算
- 多传感器: 重心控制算法
- 交叉路口: 智能识别处理

## 使用方法

### 基本调用
```c
// 简单循迹模式
Motor_Open(MOTOR_Line_Simple);

// 高级循迹模式  
Motor_Open(MOTOR_Line);
```

### 参数配置
在 `control.h` 中可调整以下参数:
```c
#define SPEED    1700    // 直线速度
#define GAIN     600     // 偏差增益
#define GAIN_K   2       // 大角度偏差增益
```

### 手动调用循迹函数
```c
// 在任务循环中调用
while(1) {
    Line_Control_Simple();  // 或 Line_Control()
    vTaskDelay(2);
}
```

## 调试建议
1. **初次使用**: 建议先使用简单模式进行调试
2. **参数调整**: 根据实际赛道调整SPEED、GAIN参数
3. **传感器检查**: 确保8路传感器正常工作
4. **电机校准**: 确保左右电机速度一致

## 注意事项
- 传感器触发逻辑为低电平有效(检测到黑线时为1)
- 电机控制采用差速转向方式
- 建议在FreeRTOS任务中使用，避免阻塞其他功能
- 循迹过程中可通过vTaskDelay调整控制频率

## 文件结构
- `Hardware/control/control.h` - 循迹控制头文件
- `Hardware/control/control.c` - 循迹控制实现
- `Hardware/LED_Key/LED_Key.h` - 传感器IO定义
- `Hardware/Motor/Motor.h` - 电机控制接口
