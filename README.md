# 8路循迹功能说明

## 功能概述
本项目实现了基于8路红外传感器的智能循迹功能，支持简单模式和高级模式两种控制策略。

## 硬件配置
- **传感器布局**: 8路红外传感器从左到右依次为 HW1-HW8
- **传感器接口**: 
  - HW1-HW6: GPIOB端口
  - HW7-HW8: GPIOB端口(upper组)
- **电机控制**: 双电机差速控制

## 传感器映射
```
传感器编号:  0    1    2    3    4    5    6    7
物理接口:   HW1  HW2  HW3  HW4  HW5  HW6  HW7  HW8
位置:      最左  左   左内  中左  中右  右内  右   最右
```

## 控制模式

### 1. 简单循迹模式 (Line_Control_Simple)
- **特点**: 简单优先级控制，易于理解和调试
- **策略**: 最外侧传感器优先级最高，依次向内递减
- **适用**: 标准赛道，简单路径

**控制逻辑**:
- 最外侧传感器(0,7): 大幅转向 (GAIN * GAIN_K)
- 外侧传感器(1,6): 中等转向 (GAIN)
- 内侧传感器(2,5): 小幅转向 (GAIN/2)
- 中间传感器(3,4): 直行

### 2. 高级PID循迹模式 (Line_Control)
- **特点**: 基于PID控制器的精确循迹
- **策略**: 使用加权传感器位置误差进行PID控制
- **适用**: 高速循迹，复杂赛道，精确控制

**PID参数**:
- Kp = 150.0 (比例系数，控制响应速度)
- Ki = 0.8 (积分系数，消除稳态误差)
- Kd = 25.0 (微分系数，减少超调)

**传感器权重**:
```
传感器:  0     1     2     3     4     5     6     7
权重:   -4.0  -2.5  -1.0  -0.3   0.3   1.0   2.5   4.0
作用:   急右  中右  小右  微调   微调  小左  中左  急左
```

**控制逻辑**:
- 位置误差计算: 加权平均传感器位置
- PID控制输出: 根据误差计算转向强度
- 脱线处理: 基于历史误差的智能搜索
- 交叉路口: 自动识别并直行通过

## 全局变量

### PID控制器访问
```c
extern struct PID line_pid;  // 循迹PID控制器全局变量

// 可以在任何包含control.h的文件中直接访问:
// line_pid.kp, line_pid.ki, line_pid.kd  - PID参数
// line_pid.err, line_pid.err_last        - 误差值
// line_pid.err_add, line_pid.out         - 积分项和输出
```

## 使用方法

### 基本调用
```c
// 简单循迹模式
Motor_Open(MOTOR_Line_Simple);

// 高级循迹模式  
Motor_Open(MOTOR_Line);
```

### 参数配置
在 `control.h` 中可调整以下参数:
```c
#define SPEED    1700    // 直线速度
#define GAIN     600     // 偏差增益
#define GAIN_K   2       // 大角度偏差增益
```

### 手动调用循迹函数
```c
// PID循迹模式
Line_PID_Init();  // 首先初始化PID参数
while(1) {
    Line_Control();  // PID循迹控制
    vTaskDelay(2);
}

// 简单循迹模式
while(1) {
    Line_Control_Simple();  // 简单循迹控制
    vTaskDelay(2);
}
```

### PID参数调试
```c
// 运行时调整PID参数
Set_Line_PID_Params(120.0f, 0.5f, 20.0f);

// 直接访问全局PID变量进行调试
extern struct PID line_pid;
line_pid.kp = 100.0f;  // 直接修改比例系数
line_pid.ki = 0.3f;    // 直接修改积分系数
line_pid.kd = 15.0f;   // 直接修改微分系数

// 获取当前位置和传感器状态
float position = Get_Line_Position();
uint8_t sensor_count = Get_Sensor_Count();
float pid_output = Get_PID_Output();

// 重置PID状态（在切换模式时使用）
Reset_Line_PID();
```

## 调试建议

### PID参数调试步骤
1. **Kp调试**: 先设置Ki=0, Kd=0，逐步增加Kp直到系统响应快速但有轻微震荡
2. **Kd调试**: 在Kp基础上增加Kd，减少震荡和超调
3. **Ki调试**: 最后添加少量Ki，消除稳态误差

### 常见问题解决
1. **震荡严重**: 减小Kp或增加Kd
2. **响应迟缓**: 增加Kp
3. **稳态误差**: 增加Ki
4. **脱线频繁**: 检查传感器安装和权重配置
5. **转弯不足**: 增加传感器权重差异

### 性能优化
1. **传感器权重**: 根据赛道特点调整权重数组
2. **控制频率**: 调整vTaskDelay时间，建议1-5ms
3. **速度限制**: 根据电机性能调整速度限幅
4. **积分限幅**: 防止积分饱和，当前限制±100

## 注意事项
- 传感器触发逻辑为低电平有效(检测到黑线时为1)
- 电机控制采用差速转向方式
- 建议在FreeRTOS任务中使用，避免阻塞其他功能
- 循迹过程中可通过vTaskDelay调整控制频率

## 文件结构
- `Hardware/control/control.h` - 循迹控制头文件
- `Hardware/control/control.c` - 循迹控制实现
- `Hardware/LED_Key/LED_Key.h` - 传感器IO定义
- `Hardware/Motor/Motor.h` - 电机控制接口
