#include "control.h"

/**************************PID����************* */
/************��Ȩ���� ���� С�з����� �����ι���***************/
#define PID_K   1.4f
struct PID Serx,Sery;
//λ��ʽPID����
void PID_Init(void)
{
	Serx.kp=-0.15;
	Serx.kd=-0.3;
	
	Sery.kp=0.35;
	Sery.kd=0.5;
}

//λ��ʽPID
float Erect_pid(struct PID* para,float hope, float now)
{
	(*para).err = now - hope;
	
	(*para).err_last_dev = (*para).err - (*para).err_last;
	
	(*para).out = (*para).kp*(*para).err + (*para).kd*(*para).err_last_dev + (*para).ki*(*para).err_add;
	
	(*para).err_last =  (*para).err;
	
	(*para).err_add+=(*para).err;
	
	return (*para).out;
}
/**************************PID����************* */

/**************************循迹控制************* */
uint8_t Line[8];  // 扩展到8路传感器

//传感器安装从左到右依次为：   0, 1, 2, 3, 4, 5, 6, 7（从左到右）
//对应模块上对应接口           HW1, HW2, HW3, HW4, HW5, HW6, HW7, HW8
void Get_hw(void)
{
	Line[0] = HW_IO1;  // 最左侧
	Line[1] = HW_IO2;  // 左侧
	Line[2] = HW_IO3;  // 左内侧
	Line[3] = HW_IO4;  // 中间左
	Line[4] = HW_IO5;  // 中间右
	Line[5] = HW_IO6;  // 右内侧
	Line[6] = HW_IO7;  // 右侧
	Line[7] = HW_IO8;  // 最右侧
}

void Line_Control(void)
{
	Get_hw();

	// 8路循迹传感器智能控制策略
	// 传感器布局：Line[0-7] 从左到右
	// 控制策略：根据传感器组合状态进行精确控制

	uint8_t sensor_count = 0;  // 触发传感器数量
	int16_t position = 0;      // 线条位置权重

	// 计算触发传感器数量和位置权重
	for(int i = 0; i < 8; i++) {
		if(Line[i] == 1) {
			sensor_count++;
			position += (i - 3.5) * 100;  // 位置权重：-350到+350
		}
	}

	// 根据传感器状态进行控制
	if(sensor_count == 0) {
		// 无传感器触发 - 保持直行或记忆上次方向
		Motor_Write(SPEED, SPEED);
	}
	else if(sensor_count == 1) {
		// 单传感器触发 - 精确转向
		if(Line[0] == 1)        // 最左侧 - 急右转
			Motor_Write(SPEED+(GAIN*GAIN_K), SPEED-(GAIN*GAIN_K));
		else if(Line[7] == 1)   // 最右侧 - 急左转
			Motor_Write(SPEED-(GAIN*GAIN_K), SPEED+(GAIN*GAIN_K));
		else if(Line[1] == 1)   // 左侧 - 大右转
			Motor_Write(SPEED+GAIN, SPEED-GAIN);
		else if(Line[6] == 1)   // 右侧 - 大左转
			Motor_Write(SPEED-GAIN, SPEED+GAIN);
		else if(Line[2] == 1)   // 左内侧 - 中右转
			Motor_Write(SPEED+(GAIN/2), SPEED-(GAIN/2));
		else if(Line[5] == 1)   // 右内侧 - 中左转
			Motor_Write(SPEED-(GAIN/2), SPEED+(GAIN/2));
		else if(Line[3] == 1 || Line[4] == 1)  // 中间 - 直行
			Motor_Write(SPEED, SPEED);
	}
	else if(sensor_count == 2) {
		// 双传感器触发 - 根据组合判断
		if((Line[3] == 1 && Line[4] == 1)) {
			// 中间两个传感器 - 完美直行
			Motor_Write(SPEED, SPEED);
		}
		else {
			// 其他双传感器组合 - 按位置权重控制
			int16_t turn_value = position / 10;  // 转向强度
			if(turn_value > GAIN) turn_value = GAIN;
			if(turn_value < -GAIN) turn_value = -GAIN;
			Motor_Write(SPEED - turn_value, SPEED + turn_value);
		}
	}
	else if(sensor_count >= 3) {
		// 多传感器触发 - 可能在交叉路口或宽线条
		if(sensor_count >= 6) {
			// 大部分传感器触发 - 可能是起始线或交叉口
			Motor_Write(SPEED, SPEED);  // 直行通过
		}
		else {
			// 3-5个传感器 - 按重心控制
			int16_t turn_value = position / (sensor_count * 5);
			if(turn_value > GAIN/2) turn_value = GAIN/2;
			if(turn_value < -GAIN/2) turn_value = -GAIN/2;
			Motor_Write(SPEED - turn_value, SPEED + turn_value);
		}
	}
}

// 简单8路循迹控制函数 - 简化版本
void Line_Control_Simple(void)
{
	Get_hw();

	// 简单优先级控制策略
	// 最外侧传感器优先级最高，依次向内递减

	if(Line[0] == 1) {
		// 最左侧传感器触发 - 大幅右转
		Motor_Write(SPEED + (GAIN * GAIN_K), SPEED - (GAIN * GAIN_K));
	}
	else if(Line[7] == 1) {
		// 最右侧传感器触发 - 大幅左转
		Motor_Write(SPEED - (GAIN * GAIN_K), SPEED + (GAIN * GAIN_K));
	}
	else if(Line[1] == 1) {
		// 左侧传感器触发 - 中等右转
		Motor_Write(SPEED + GAIN, SPEED - GAIN);
	}
	else if(Line[6] == 1) {
		// 右侧传感器触发 - 中等左转
		Motor_Write(SPEED - GAIN, SPEED + GAIN);
	}
	else if(Line[2] == 1) {
		// 左内侧传感器触发 - 小幅右转
		Motor_Write(SPEED + (GAIN/2), SPEED - (GAIN/2));
	}
	else if(Line[5] == 1) {
		// 右内侧传感器触发 - 小幅左转
		Motor_Write(SPEED - (GAIN/2), SPEED + (GAIN/2));
	}
	else if(Line[3] == 1 || Line[4] == 1) {
		// 中间传感器触发 - 直行
		Motor_Write(SPEED, SPEED);
	}
	else {
		// 无传感器触发 - 保持直行
		Motor_Write(SPEED, SPEED);
	}
}

/*************************
:Բͬʽ�������
��ڲ���:
Mode:            ��ѡ��Ѳ��or��ת
����ֵ:��
*************************/
void Motor_Open(int Mode)
{
	if(Mode == MOTOR_Line)                                 //高级循迹模式
	{
		//智能循迹
        while(1)
        {
            //智能循迹控制
            Line_Control();
            vTaskDelay(2);
        }
        Motor_Write(SPEED,SPEED);
        vTaskDelay(40);
	}
	else if(Mode == MOTOR_Line_Simple)                     //简单循迹模式
	{
		//简单循迹
        while(1)
        {
            //简单循迹控制
            Line_Control_Simple();
            vTaskDelay(2);
        }
        Motor_Write(SPEED,SPEED);
        vTaskDelay(40);
	}
	else if(Mode == MOTOR_TURN)
	{

        // while(Distance_limit(distance_hope,distance_count))
        // {
        //     if(dir == LEFT_OR)	Motor_Write(-SPEED,SPEED);
        //     else if(dir == RIGHT_OR)	Motor_Write(SPEED,-SPEED);
        //     vTaskDelay(2);
        // }
        
        // if(dir == LEFT_OR) 
        // {
        //     //�ȴ�С����ת���Ҷ�ģ�鴥������
        //     while(HW_IO2) vTaskDelay(2);
        //     Motor_Write(0,0);  //���ͣת
        //     vTaskDelay(20);
        // }
        // else
        // {
        //     while(HW_IO3) vTaskDelay(2);
        //     Motor_Write(0,0);  //���ͣת
        //     vTaskDelay(20);
        // }
		
	}
	
	// Motor_Write(0,0);  //���ͣת
}
/**************************ѭ������************* */


