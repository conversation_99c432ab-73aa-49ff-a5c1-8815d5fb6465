#include "control.h"

/**************************PID����************* */
/************��Ȩ���� ���� С�з����� �����ι���***************/
#define PID_K   1.4f
struct PID Serx,Sery;
//位置式PID初始化
void PID_Init(void)
{
	Serx.kp=-0.15;
	Serx.kd=-0.3;

	Sery.kp=0.35;
	Sery.kd=0.5;
}

/**************************循迹PID控制************* */

//λ��ʽPID
float Erect_pid(struct PID* para,float hope, float now)
{
	(*para).err = now - hope;
	
	(*para).err_last_dev = (*para).err - (*para).err_last;
	
	(*para).out = (*para).kp*(*para).err + (*para).kd*(*para).err_last_dev + (*para).ki*(*para).err_add;
	
	(*para).err_last =  (*para).err;
	
	(*para).err_add+=(*para).err;
	
	return (*para).out;
}
/**************************PID����************* */

/**************************循迹控制************* */
// 8路传感器状态数组
uint8_t Line[8];

// 8路传感器权重数组 (从左到右，负值表示左偏，正值表示右偏)
static const float sensor_weights[8] = {
    -4.0f,  // 传感器0 (最左侧) - 强负权重，急右转
    -2.5f,  // 传感器1 (左侧)   - 中负权重，中右转
    -1.0f,  // 传感器2 (左内侧) - 弱负权重，小右转
    -0.3f,  // 传感器3 (中左)   - 微负权重，微调
    0.3f,   // 传感器4 (中右)   - 微正权重，微调
    1.0f,   // 传感器5 (右内侧) - 弱正权重，小左转
    2.5f,   // 传感器6 (右侧)   - 中正权重，中左转
    4.0f    // 传感器7 (最右侧) - 强正权重，急左转
};

// 循迹PID控制器
static struct PID line_pid = {0};

//传感器安装从左到右依次为：   0, 1, 2, 3, 4, 5, 6, 7（从左到右）
//对应模块上对应接口           HW1, HW2, HW3, HW4, HW5, HW6, HW7, HW8
void Get_hw(void)
{
    Line[0] = HW_IO1;  // 最左侧
    Line[1] = HW_IO2;  // 左侧
    Line[2] = HW_IO3;  // 左内侧
    Line[3] = HW_IO4;  // 中间左
    Line[4] = HW_IO5;  // 中间右
    Line[5] = HW_IO6;  // 右内侧
    Line[6] = HW_IO7;  // 右侧
    Line[7] = HW_IO8;  // 最右侧
}

// 循迹PID参数初始化
void Line_PID_Init(void)
{
    // PID参数设置 - 针对循迹优化
    line_pid.kp = 0.0f;  // 比例系数：控制响应速度，值越大响应越快
    line_pid.ki = 0.0f;    // 积分系数：消除稳态误差，防止长期偏移
    line_pid.kd = 0.0f;   // 微分系数：减少超调，提高稳定性

    // 清零PID状态变量
    line_pid.err = 0.0f;
    line_pid.err_last = 0.0f;
    line_pid.err_last_dev = 0.0f;
    line_pid.err_add = 0.0f;
    line_pid.out = 0.0f;
}
float pid_output;
// 基于PID的8路循迹控制主函数
void Line_Control(void)
{
    Get_hw();  // 读取8路传感器状态

    // 8路循迹传感器PID控制策略
    // 传感器布局：Line[0-7] 从左到右
    // 权重：-4.0, -2.5, -1.0, -0.3, 0.3, 1.0, 2.5, 4.0

    float position_error = 0.0f;  // 位置误差
    uint8_t sensor_count = 0;     // 触发传感器数量
    float weighted_sum = 0.0f;    // 加权和
    static float last_error = 0.0f;  // 上次误差，用于无传感器时的记忆

    // 计算加权位置误差
    for(int i = 0; i < 8; i++) {
        if(Line[i] == 1) {
            sensor_count++;
            weighted_sum += sensor_weights[i];
        }
    }

    // 计算位置误差
    if(sensor_count > 0) {
        position_error = weighted_sum / sensor_count;  // 平均加权位置
        last_error = position_error;  // 记录有效误差
    } else {
        // 无传感器触发时，使用上次误差继续控制
        position_error = last_error;
    }

    // 特殊情况处理
    if(sensor_count == 0) {
        // 完全脱线 - 根据上次误差方向继续搜索
        if(last_error > 0) {
            // 上次右偏，继续左转搜索
            Motor_Write(SPEED - GAIN, SPEED + GAIN);
        } else if(last_error < 0) {
            // 上次左偏，继续右转搜索
            Motor_Write(SPEED + GAIN, SPEED - GAIN);
        } else {
            // 无历史信息，保持直行
            Motor_Write(SPEED, SPEED);
        }
        return;
    }

    if(sensor_count >= 6) {
        // 大部分传感器触发 - 可能在起始线、终点线或交叉口
        Motor_Write(SPEED, SPEED);  // 直行通过
        return;
    }

    // 正常循迹 - 使用PID控制
     pid_output = Erect_pid(&line_pid, 0.0f, position_error);

    // PID输出限幅
    if(pid_output > GAIN * 1.5f) pid_output = GAIN * 1.5f;
    if(pid_output < -GAIN * 1.5f) pid_output = -GAIN * 1.5f;

    // 计算左右电机速度
    int16_t left_speed = SPEED - (int16_t)pid_output;
    int16_t right_speed = SPEED + (int16_t)pid_output;

    // 电机速度限制，确保不反转且不超速
    if(left_speed < SPEED/3) left_speed = SPEED/3;      // 最低速度限制
    if(right_speed < SPEED/3) right_speed = SPEED/3;    // 最低速度限制
    if(left_speed > SPEED * 1.8f) left_speed = SPEED * 1.8f;   // 最高速度限制
    if(right_speed > SPEED * 1.8f) right_speed = SPEED * 1.8f; // 最高速度限制

    // 输出到电机
    Motor_Write(left_speed, right_speed);
}

// 获取线条位置函数 - 返回加权平均位置
float Get_Line_Position(void)
{
    Get_hw();  // 读取传感器状态

    float weighted_sum = 0.0f;
    uint8_t sensor_count = 0;

    // 计算加权位置
    for(int i = 0; i < 8; i++) {
        if(Line[i] == 1) {
            sensor_count++;
            weighted_sum += sensor_weights[i];
        }
    }

    // 返回平均位置，无传感器时返回0
    return (sensor_count > 0) ? (weighted_sum / sensor_count) : 0.0f;
}

// 获取触发传感器数量
uint8_t Get_Sensor_Count(void)
{
    Get_hw();  // 读取传感器状态

    uint8_t count = 0;
    for(int i = 0; i < 8; i++) {
        if(Line[i] == 1) {
            count++;
        }
    }

    return count;
}

// PID参数调整函数 - 运行时动态调整PID参数
void Set_Line_PID_Params(float kp, float ki, float kd)
{
    line_pid.kp = kp;
    line_pid.ki = ki;
    line_pid.kd = kd;
}

// 获取当前PID输出值 - 用于调试
float Get_PID_Output(void)
{
    return line_pid.out;
}

// 重置PID控制器状态 - 清除积分项和历史误差
void Reset_Line_PID(void)
{
    line_pid.err = 0.0f;
    line_pid.err_last = 0.0f;
    line_pid.err_last_dev = 0.0f;
    line_pid.err_add = 0.0f;
    line_pid.out = 0.0f;
}

// 简单8路循迹控制函数 - 简化版本
void Line_Control_Simple(void)
{
	Get_hw();

	// 简单优先级控制策略
	// 最外侧传感器优先级最高，依次向内递减

	if(Line[0] == 1) {
		// 最左侧传感器触发 - 大幅右转
		Motor_Write(SPEED + (GAIN * GAIN_K), SPEED - (GAIN * GAIN_K));
	}
	else if(Line[7] == 1) {
		// 最右侧传感器触发 - 大幅左转
		Motor_Write(SPEED - (GAIN * GAIN_K), SPEED + (GAIN * GAIN_K));
	}
	else if(Line[1] == 1) {
		// 左侧传感器触发 - 中等右转
		Motor_Write(SPEED + GAIN, SPEED - GAIN);
	}
	else if(Line[6] == 1) {
		// 右侧传感器触发 - 中等左转
		Motor_Write(SPEED - GAIN, SPEED + GAIN);
	}
	else if(Line[2] == 1) {
		// 左内侧传感器触发 - 小幅右转
		Motor_Write(SPEED + (GAIN/2), SPEED - (GAIN/2));
	}
	else if(Line[5] == 1) {
		// 右内侧传感器触发 - 小幅左转
		Motor_Write(SPEED - (GAIN/2), SPEED + (GAIN/2));
	}
	else if(Line[3] == 1 || Line[4] == 1) {
		// 中间传感器触发 - 直行
		Motor_Write(SPEED, SPEED);
	}
	else {
		// 无传感器触发 - 保持直行
		Motor_Write(SPEED, SPEED);
	}
}

/*************************
:Բͬʽ�������
��ڲ���:
Mode:            ��ѡ��Ѳ��or��ת
����ֵ:��
*************************/
void Motor_Open(int Mode)
{
	if(Mode == MOTOR_Line)                                 //高级循迹模式
	{
		// 初始化PID控制器
		Line_PID_Init();

		// 重置PID状态
		Reset_Line_PID();

		// 智能PID循迹
        while(1)
        {
            // 智能PID循迹控制
            Line_Control();
            vTaskDelay(2);
        }
        Motor_Write(SPEED,SPEED);
        vTaskDelay(40);
	}
	else if(Mode == MOTOR_Line_Simple)                     //简单循迹模式
	{
		// 简单循迹
        while(1)
        {
            // 简单循迹控制
            Line_Control_Simple();
            vTaskDelay(2);
        }
        Motor_Write(SPEED,SPEED);
        vTaskDelay(40);
	}
	else if(Mode == MOTOR_TURN)
	{

        // while(Distance_limit(distance_hope,distance_count))
        // {
        //     if(dir == LEFT_OR)	Motor_Write(-SPEED,SPEED);
        //     else if(dir == RIGHT_OR)	Motor_Write(SPEED,-SPEED);
        //     vTaskDelay(2);
        // }
        
        // if(dir == LEFT_OR) 
        // {
        //     //�ȴ�С����ת���Ҷ�ģ�鴥������
        //     while(HW_IO2) vTaskDelay(2);
        //     Motor_Write(0,0);  //���ͣת
        //     vTaskDelay(20);
        // }
        // else
        // {
        //     while(HW_IO3) vTaskDelay(2);
        //     Motor_Write(0,0);  //���ͣת
        //     vTaskDelay(20);
        // }
		
	}
	
	// Motor_Write(0,0);  //���ͣת
}
/**************************ѭ������************* */


